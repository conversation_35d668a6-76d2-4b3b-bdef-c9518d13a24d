# Solana Token Price Tracker Bot

A Telegram bot that tracks Solana token prices and sends notifications when they reach significant price movements.

## Features

- Tracks Solana token prices using GeckoTerminal API
- Sends alerts for price increases at:
  - 50%, 100%, 200% thresholds
  - Every additional 100% up to 20,000%
- Automatically removes tokens after 24 hours
- Real-time monitoring with 30-second updates
- Multiple ways to input tokens:
  - Direct paste of token address
  - Token links from GeckoTerminal, Solscan, Birdeye
  - Using /track command
- Manual refresh option

## Docker Setup

### Build and Run with Docker

1. Build the Docker image:
   ```bash
   docker build -t solana-price-tracker .
   ```

2. Run the container:
   ```bash
   docker run -d --name solana-tracker -e TELEGRAM_BOT_TOKEN=your_telegram_bot_token solana-price-tracker
   ```

### Using Docker Compose

1. Create a `.env` file with your Telegram Bot token:
   ```
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   ```

2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## Bot Commands

- `/start` - Start the bot and get welcome message
- `/track <token_address>` - Start tracking a Solana token
- `/list` - List all tokens you're currently tracking
- `/status` - Check current status and progress of tracked tokens
- `/status refresh` - Update prices of all your tracked tokens
- `/untrack <token_address>` - Stop tracking a token
- `/clear` - Remove all tokens you're tracking (requires confirmation)
- `/help` - Show help message

## Alternative: Running Without Docker

1. Install dependencies:
   ```bash
   pip install -r docker-requirements.txt
   ```

2. Run the bot:
   ```bash
   export TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   python run_bot.py
   ```