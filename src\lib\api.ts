import axios from 'axios'
import { PumpDetail } from '../types/gmgn-ai-types'
import { TokenInfoPump } from '../types/pumpfun-types'
import { HeliusTransaction } from '../types/helius-types'

// this class is no longer used in this project but some of these apis can be useful
export class ApiRequests {
  constructor() {}

  public async gmgnTokenInfo(addr: string): Promise<PumpDetail | undefined> {
    try {
      const res = await fetch(`https://gmgn.ai/defi/quotation/v1/tokens/sol/${addr}`)
      const data = await res.json()

      if (data.code === 0) {
        return data.data.token
      }

      return
    } catch (error) {
      console.log('GMGN_API_ERROR', error)
      return
    }
  }

  public async pumpFunTokenInfo(addr: string): Promise<TokenInfoPump | undefined> {
    try {
      const url = `https://frontend-api.pump.fun/coins/${addr}`
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0',
          Accept: '*/*',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          Referer: 'https://www.pump.fun/',
          Origin: 'https://www.pump.fun',
          Connection: 'keep-alive',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'cross-site',
          'If-None-Match': 'W/"43a-tWaCcS4XujSi30IFlxDCJYxkMKg"',
        },
      })
      if (response.status === 200) {
        return response.data
      } else {
        console.error('Failed to retrieve coin data:', response.status)
        return
      }
    } catch (error) {
      console.error('Error fetching coin data:', error)
      return
    }
  }

  static async parseTransactionWithHelius(
    transactionSignature: string,
  ): Promise<{ message: string; type: 'buy' | 'sell' } | undefined> {
    // Get available Helius API keys
    const heliusKeys = process.env.HELIUS_API_KEYS?.split(',').map(key => key.trim()).filter(Boolean) || []

    // Fallback to single key for backward compatibility
    if (heliusKeys.length === 0 && process.env.HELIUS_API_KEY) {
      heliusKeys.push(process.env.HELIUS_API_KEY)
    }

    if (heliusKeys.length === 0) {
      console.error('No Helius API keys configured')
      return undefined
    }

    // Try with different API keys if one fails
    for (let i = 0; i < heliusKeys.length; i++) {
      const apiKey = heliusKeys[i]
      const apiUrl = `https://api.helius.xyz/v0/transactions/?api-key=${apiKey}`
      console.log(`Parsing Transaction with Helius key ${i + 1}/${heliusKeys.length}:`, transactionSignature)

      try {
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            transactions: [transactionSignature],
          }),
        })

        if (!response.ok) {
          throw new Error(`Error: ${response.status} ${response.statusText}`)
        }

        const transactions = (await response.json()) as HeliusTransaction[]
        console.log('Received transactions:', transactions)
        const type: 'buy' | 'sell' = transactions[0]!.accountData[0]!.nativeBalanceChange > 0 ? 'sell' : 'buy'

        return {
          message: transactions[0]!.description,
          type,
        }
      } catch (error) {
        console.error(`Error parsing transaction with Helius key ${i + 1}:`, error)

        // If this is the last key, return undefined
        if (i === heliusKeys.length - 1) {
          console.error('All Helius API keys failed for transaction parsing')
          return undefined
        }

        // Continue to next API key
        console.log(`Trying next Helius API key...`)
      }
    }

    return undefined
  }
}
