import logging
import os
from dotenv import load_dotenv
from bot import run_bot

# Load environment variables from .env file
load_dotenv()

# Set up logging
log_level = os.environ.get("LOG_LEVEL", "DEBUG")
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=getattr(logging, log_level)
)

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting Solana Token Price Tracker Bot directly")
    run_bot()