# Notification System Documentation

This document provides detailed information about the notification system used in the Solana Token Price Tracker bot, including thresholds, success fields, and recommendations for optimization.

## Table of Contents

1. [Notification Overview](#notification-overview)
2. [Price Increase Thresholds](#price-increase-thresholds)
3. [Price Decrease Thresholds](#price-decrease-thresholds)
4. [Success Fields](#success-fields)
5. [Notification Format](#notification-format)
6. [Recommendations](#recommendations)

## Notification Overview

The Solana Token Price Tracker bot implements a notification system that alerts users when token prices reach specific thresholds. The system tracks both price increases and decreases, with different notification strategies for each.

## Price Increase Thresholds

The bot notifies users when token prices increase by specific percentages from the initial tracking price.

| Threshold | Description | Implementation |
|-----------|-------------|----------------|
| 100% | Token price doubles | Key notification threshold |
| 300% | Token price quadruples | Major milestone |
| 500% | 6x initial price | Significant growth |
| 1000% | 11x initial price | Major growth milestone |
| 2000% | 21x initial price | Exceptional growth |
| 5000% | 51x initial price | Rare growth milestone |
| 7000% | 71x initial price | Extremely rare growth |
| 10000% | 101x initial price | Highest tracked milestone |

### Implementation Details

```python
# Define specific notification thresholds
self.notification_thresholds = [100, 300, 500, 1000, 2000, 5000, 7000, 10000]

# Check notification thresholds for price increases
for threshold in self.notification_thresholds:
    threshold_key = str(threshold)
    # Check if we have reached this threshold and haven't notified yet
    if pct_change >= threshold and not data["notified_thresholds"].get(threshold_key, True):
        await self._send_notification(bot, chat_id, token_address, token_name, threshold, current_price, initial_price)
        self.tracked_tokens[token_address][chat_id]["notified_thresholds"][threshold_key] = True

        # If we reach 100%, mark the special flag for the -35% condition
        if threshold == 100:
            self.tracked_tokens[token_address][chat_id]["reached_100_first"] = True
```

## Price Decrease Thresholds

The bot also notifies users when token prices decrease significantly, with a special condition based on whether the token has previously reached the 100% increase threshold.

| Threshold | Description | Condition |
|-----------|-------------|-----------|
| -35% | Price drops by 35% | Only if token hasn't reached 100% increase first |

### Implementation Details

```python
# Define negative threshold for price drop alert
self.negative_threshold = -35

# Check for price decrease notification
if (pct_change <= self.negative_threshold and 
    not data["notified_negative_35"] and 
    not data["reached_100_first"]):
    await self._send_negative_notification(bot, chat_id, token_address, token_name, 
                                          self.negative_threshold, current_price, initial_price)
    self.tracked_tokens[token_address][chat_id]["notified_negative_35"] = True
```

## Success Fields

The application uses consistent success fields in API responses and function returns to indicate the status of operations.

### API Response Success Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| success | boolean | Indicates if the operation was successful | `"success": true` |
| data | object | Contains the operation result data | `"data": { "price": 0.123 }` |
| error | string | Error message if operation failed | `"error": "Token not found"` |

### Token Tracking Success Fields

| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if token tracking was successful |
| data | object | Contains token information if successful |
| data.address | string | Token address |
| data.name | string | Token name |
| data.symbol | string | Token symbol |
| data.price | number | Current token price |
| data.initial_price | number | Initial token price when tracking started |
| error | string | Error message if tracking failed |

### Example Success Response

```json
{
  "success": true,
  "data": {
    "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "name": "USD Coin",
    "symbol": "USDC",
    "price": 1.0,
    "initial_price": 1.0
  }
}
```

### Example Error Response

```json
{
  "success": false,
  "error": "Failed to fetch token data: Token not found"
}
```

## Notification Format

The bot sends formatted notifications to users when price thresholds are reached.

### Price Increase Notification

```
🚀 PRICE ALERT: {token_name} ({token_address})

Increased by {threshold}%!
Initial price: ${initial_price}
Current price: ${current_price}

Keep tracking this token for more alerts!
```

### Price Decrease Notification

```
📉 PRICE DROP ALERT: {token_name} ({token_address})

Decreased by {abs(threshold)}%!
Initial price: ${initial_price}
Current price: ${current_price}

This token might be losing momentum.
```

## Recommendations

Based on analysis of the notification system, here are recommendations for optimization:

### Threshold Optimization

1. **Customizable Thresholds**: Allow users to set their own notification thresholds
2. **Dynamic Thresholds**: Adjust thresholds based on token volatility
3. **Time-Based Thresholds**: Add notifications for rapid price changes within short time periods

### Success Field Standardization

1. **Consistent Format**: Maintain the current success/data/error format across all functions
2. **Detailed Error Codes**: Add error codes for more specific error handling
3. **Timestamp**: Include timestamp in all responses for tracking purposes

### Notification Enhancements

1. **Rich Notifications**: Add charts or graphs to notifications
2. **Trend Analysis**: Include trend information in notifications
3. **Volume Alerts**: Add notifications for significant volume changes
4. **Comparative Data**: Include market comparison data in notifications

### System Improvements

1. **Notification Batching**: Batch notifications to prevent spam
2. **Notification History**: Store notification history for user reference
3. **Notification Preferences**: Allow users to customize notification format and frequency
4. **Notification Channels**: Add support for additional notification channels (email, Discord, etc.)

### Performance Optimization

1. **Caching**: Cache token data to reduce API calls
2. **Parallel Processing**: Process token price checks in parallel
3. **Scheduled Notifications**: Group notifications into scheduled batches
4. **Rate Limiting**: Implement rate limiting for notifications per user
