2025-05-29 13:05:46 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:05:46 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:05:46 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:14:40 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:14:41 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:14:41 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:19:20 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:19:21 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:19:21 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:19:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:25:25 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:25:26 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:25:26 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:26:46 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"bigboss1","userCount":1}}
2025-05-29 13:26:46 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 13:27:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4EMX9A8UpbHQLsX63HC78ozkapruvox951zZxXBE3KTrzdQnFWfop2vZzGeNqHwkommx893aUQyGzSkYgnFNkNxU","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"581rsw7WwheLYhbTVzKTgGp3wmy65W3CbYmDgBMJViJmpWXVLzc9K8yZzBXV9PVKTWBD6jD5Y3AWUY5Wu2v56wuy","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JPPxQ8AhtCPXGkcyMQjWKync5DJqkb4frb1vtX2WuVoWXMCHZcWVzoC7RxZDiJWpedhVRBckhrDHcPUUexGFtH1","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:10 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:10 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:10 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"3NLJ6My9zx8HDREREff4yUt8ozTWn8mGxvPbyeyQkMjTyFgvpWbbK1GGwRKCoJqjWVd8Wqf4b8TjPLGTtUo5py3X","metadata":{"attempt":1,"duration":445}}
2025-05-29 13:27:10 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:10 DEBUG [transaction-fetcher] No transaction details found for 5QuqHGkJ3ZfqX6rcNsfXThGY8Jc6RHjvYZsKC8RLkg5yjTEQRrWZJ8PDhXEvh6ZyfcJcBPdiUsCKnFVGgDwwZWBN {"transactionSignature":"5QuqHGkJ3ZfqX6rcNsfXThGY8Jc6RHjvYZsKC8RLkg5yjTEQRrWZJ8PDhXEvh6ZyfcJcBPdiUsCKnFVGgDwwZWBN","metadata":{"attempt":1,"retries":4}}
2025-05-29 13:27:11 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:11 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:11 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:11 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:11 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5QuqHGkJ3ZfqX6rcNsfXThGY8Jc6RHjvYZsKC8RLkg5yjTEQRrWZJ8PDhXEvh6ZyfcJcBPdiUsCKnFVGgDwwZWBN","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:20 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3VnfEdeezxVbfVESjsQUCiZEsWjN3C18VdRAmj4rbjfwxq1k2XRwpR17AySqhnZ85LBYQo3q2U6rKjXofDs3CpZU","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41c0dfeb-cdf8-4a34-8b7a-ae98d2291ebb\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41c0dfeb-cdf8-4a34-8b7a-ae98d2291ebb\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:21 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JPPxQ8AhtCPXGkcyMQjWKync5DJqkb4frb1vtX2WuVoWXMCHZcWVzoC7RxZDiJWpedhVRBckhrDHcPUUexGFtH1","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:27:21 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4EMX9A8UpbHQLsX63HC78ozkapruvox951zZxXBE3KTrzdQnFWfop2vZzGeNqHwkommx893aUQyGzSkYgnFNkNxU","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:27:21 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:22 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"581rsw7WwheLYhbTVzKTgGp3wmy65W3CbYmDgBMJViJmpWXVLzc9K8yZzBXV9PVKTWBD6jD5Y3AWUY5Wu2v56wuy","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"80a0d0ed-8cc2-4ddd-b044-135a61a8d4f6\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"80a0d0ed-8cc2-4ddd-b044-135a61a8d4f6\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:23 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JPPxQ8AhtCPXGkcyMQjWKync5DJqkb4frb1vtX2WuVoWXMCHZcWVzoC7RxZDiJWpedhVRBckhrDHcPUUexGFtH1","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:24 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5QuqHGkJ3ZfqX6rcNsfXThGY8Jc6RHjvYZsKC8RLkg5yjTEQRrWZJ8PDhXEvh6ZyfcJcBPdiUsCKnFVGgDwwZWBN","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3e6ba9fe-0338-4fb2-aedb-77441669c9e9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3e6ba9fe-0338-4fb2-aedb-77441669c9e9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:26 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:27 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:27 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5QuqHGkJ3ZfqX6rcNsfXThGY8Jc6RHjvYZsKC8RLkg5yjTEQRrWZJ8PDhXEvh6ZyfcJcBPdiUsCKnFVGgDwwZWBN","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:30 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3VnfEdeezxVbfVESjsQUCiZEsWjN3C18VdRAmj4rbjfwxq1k2XRwpR17AySqhnZ85LBYQo3q2U6rKjXofDs3CpZU","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"69478934-d15a-4d45-94ba-8ff49c41c6f8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"69478934-d15a-4d45-94ba-8ff49c41c6f8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:30 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:30 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:32 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4EMX9A8UpbHQLsX63HC78ozkapruvox951zZxXBE3KTrzdQnFWfop2vZzGeNqHwkommx893aUQyGzSkYgnFNkNxU","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"aeab2c62-134a-497f-aa5f-ac55db1b9325\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"aeab2c62-134a-497f-aa5f-ac55db1b9325\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:33 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:35 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"581rsw7WwheLYhbTVzKTgGp3wmy65W3CbYmDgBMJViJmpWXVLzc9K8yZzBXV9PVKTWBD6jD5Y3AWUY5Wu2v56wuy","metadata":{"attempt":3,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:27:35 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:36 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4EMX9A8UpbHQLsX63HC78ozkapruvox951zZxXBE3KTrzdQnFWfop2vZzGeNqHwkommx893aUQyGzSkYgnFNkNxU","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JPPxQ8AhtCPXGkcyMQjWKync5DJqkb4frb1vtX2WuVoWXMCHZcWVzoC7RxZDiJWpedhVRBckhrDHcPUUexGFtH1","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:27:38 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:38 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"581rsw7WwheLYhbTVzKTgGp3wmy65W3CbYmDgBMJViJmpWXVLzc9K8yZzBXV9PVKTWBD6jD5Y3AWUY5Wu2v56wuy","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:39 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3jtS2VebKL9b2j1VKHhrjopCLmP8A8xc7BvpFbRLyMptKFa1UW3K43F88aYDKuUzvF34NqYbnHPBu5Y3fY3rfHN8","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3c594829-f357-4465-b18b-b135538efbb7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3c594829-f357-4465-b18b-b135538efbb7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:39 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5uqdsL42txRaci3ADT9SGDE5oPEhx7rHv4qSjfHmvidMBCftqS5TvpGKqjVQBPBoggMkWFm14R77kq44BSSJPaER","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0771616a-9c16-47f9-8aa9-2591e6e33eec\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0771616a-9c16-47f9-8aa9-2591e6e33eec\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:41 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3VnfEdeezxVbfVESjsQUCiZEsWjN3C18VdRAmj4rbjfwxq1k2XRwpR17AySqhnZ85LBYQo3q2U6rKjXofDs3CpZU","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"23c512c7-76b0-490a-a31e-6db10ad0aa0d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"23c512c7-76b0-490a-a31e-6db10ad0aa0d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:41 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4xYhWmZV9jFQ2vfvz8CXmee7eQBP6DwBqbRPbAbMAMM14LhKc1f26q3cd2NxftmW4BqpiWicvSUZNR75RxWdAE8F","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"fd2814c5-5eb6-4143-805c-4e6d65ed7b31\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"fd2814c5-5eb6-4143-805c-4e6d65ed7b31\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:42 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:27:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:44 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3VnfEdeezxVbfVESjsQUCiZEsWjN3C18VdRAmj4rbjfwxq1k2XRwpR17AySqhnZ85LBYQo3q2U6rKjXofDs3CpZU","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:49 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3jtS2VebKL9b2j1VKHhrjopCLmP8A8xc7BvpFbRLyMptKFa1UW3K43F88aYDKuUzvF34NqYbnHPBu5Y3fY3rfHN8","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"71c52bd0-f3c6-4de1-981e-3245e185ac7d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"71c52bd0-f3c6-4de1-981e-3245e185ac7d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:51 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5uqdsL42txRaci3ADT9SGDE5oPEhx7rHv4qSjfHmvidMBCftqS5TvpGKqjVQBPBoggMkWFm14R77kq44BSSJPaER","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:27:51 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:51 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4xYhWmZV9jFQ2vfvz8CXmee7eQBP6DwBqbRPbAbMAMM14LhKc1f26q3cd2NxftmW4BqpiWicvSUZNR75RxWdAE8F","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"31009fd0-6b6d-461d-b9d6-03ad359afcd5\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"31009fd0-6b6d-461d-b9d6-03ad359afcd5\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:53 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:27:53 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:27:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4xYhWmZV9jFQ2vfvz8CXmee7eQBP6DwBqbRPbAbMAMM14LhKc1f26q3cd2NxftmW4BqpiWicvSUZNR75RxWdAE8F","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:27:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3jtS2VebKL9b2j1VKHhrjopCLmP8A8xc7BvpFbRLyMptKFa1UW3K43F88aYDKuUzvF34NqYbnHPBu5Y3fY3rfHN8","metadata":{"attempt":3,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5uqdsL42txRaci3ADT9SGDE5oPEhx7rHv4qSjfHmvidMBCftqS5TvpGKqjVQBPBoggMkWFm14R77kq44BSSJPaER","metadata":{"attempt":3,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:04 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:06 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4xYhWmZV9jFQ2vfvz8CXmee7eQBP6DwBqbRPbAbMAMM14LhKc1f26q3cd2NxftmW4BqpiWicvSUZNR75RxWdAE8F","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:15 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3jtS2VebKL9b2j1VKHhrjopCLmP8A8xc7BvpFbRLyMptKFa1UW3K43F88aYDKuUzvF34NqYbnHPBu5Y3fY3rfHN8","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5uqdsL42txRaci3ADT9SGDE5oPEhx7rHv4qSjfHmvidMBCftqS5TvpGKqjVQBPBoggMkWFm14R77kq44BSSJPaER","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:30 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"3wodQBTRztzfvSh1pABynRntKeYvJBeNH8yNRFgmML5DwHtAkJFRyp9VSj5fAzHMpGNRH8PeWRqaTXYBeHS5QApg","metadata":{"attempt":1,"duration":332}}
2025-05-29 13:28:30 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:30 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2yU3Af7ZrBrXDdTB7zCHBoM2wsh8Y7JFTxU7FNMCoCchSuTdpQrwSXjr53Uq1jbH8hYhfh7w5dgMCqphBXshdf4f","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:30 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3mM2vhm86cxmW2XMpAFehZqZDC7Pkogq5zgdb4SyGfsz7BRFEq8QKnz3xWjE9aqd9gAgAAukc2oifGR9yfVcLsNS","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:30 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54cSr4h2VMupkSLruxTQWa8CBQJ5K1qN5eKnYhD9vxGik9mMHd966HUtehG5gWjJqE1LrRErTwmMYsYiTKQ96Tn2","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:30 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"5FyQsBVoZE8sTvN6k6dup5MSnRTsv2XjZoD3f9F3Ev3iAoD1Scnig8sdK8ymTsfUkmYDPkuixYF6TxfUS6onXQbW","metadata":{"attempt":1,"duration":481}}
2025-05-29 13:28:30 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:31 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:31 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:31 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:31 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3mM2vhm86cxmW2XMpAFehZqZDC7Pkogq5zgdb4SyGfsz7BRFEq8QKnz3xWjE9aqd9gAgAAukc2oifGR9yfVcLsNS","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:33 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:39 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54cSr4h2VMupkSLruxTQWa8CBQJ5K1qN5eKnYhD9vxGik9mMHd966HUtehG5gWjJqE1LrRErTwmMYsYiTKQ96Tn2","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c57f2b99-672c-4d9e-b873-a96d267714b1\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c57f2b99-672c-4d9e-b873-a96d267714b1\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5C9JcUPbubhq28716hz52R5pDBqg4aaa6Dk6Bx4XvZf7yEJG2x2xd7zTDoaLMURj9MH3GjssKsxWGWHjLEmNz2VM","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"22VTKmPmpir4r1DhwjdQjivAkSAPYhyMEgynTNaxWLsLtjd9daAZmivr32cDiwGeD1uwZaaB28USU3gekXWGUWS7","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4SAJUNvhNaXXuF9mWPtVEqZkWTMYFm9r2u6mwJftSxd5yY7xNAzK5tkN5USoACVwEty3cUYCwNgjD6rAZHonMgpp","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JAZExJrWfLQtQXxRvYbzD1mS8gM6Hawfo9M5L43s3naNftcBaN3m6YayjKWcyiGygvkzZSLTEfDVKk6Mpt8myKW","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U2GfMiMKYQcGvU6CUFWG68NNp7AyWhTUvqa6p2o2FiXzYQbV4cjpPrq7ZY8gmrKAGTLLmPk1GF1BbY9aZPcVFw5","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"66rB8VoJwYasYUp3Wttbv4jnSmGPKsN7ZFGRfxRxAc8mHpXmcUzuPgCsbbnX4QkneX9NucFnJfmctGF7iXcj2eF7","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:40 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4hSZSyyDh8ZS87ok3FqufeSE1q47x7Nqj9uiVYyCmNTsM6GBmdS4x3AwYp5gJE4ByJveNdYtDxfq2RMwArp8GC7t","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:41 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2yU3Af7ZrBrXDdTB7zCHBoM2wsh8Y7JFTxU7FNMCoCchSuTdpQrwSXjr53Uq1jbH8hYhfh7w5dgMCqphBXshdf4f","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:41 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:42 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54cSr4h2VMupkSLruxTQWa8CBQJ5K1qN5eKnYhD9vxGik9mMHd966HUtehG5gWjJqE1LrRErTwmMYsYiTKQ96Tn2","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:42 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3mM2vhm86cxmW2XMpAFehZqZDC7Pkogq5zgdb4SyGfsz7BRFEq8QKnz3xWjE9aqd9gAgAAukc2oifGR9yfVcLsNS","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"add27df4-fecd-49c3-ac82-2506971020c4\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"add27df4-fecd-49c3-ac82-2506971020c4\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:43 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:43 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2yU3Af7ZrBrXDdTB7zCHBoM2wsh8Y7JFTxU7FNMCoCchSuTdpQrwSXjr53Uq1jbH8hYhfh7w5dgMCqphBXshdf4f","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:45 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3mM2vhm86cxmW2XMpAFehZqZDC7Pkogq5zgdb4SyGfsz7BRFEq8QKnz3xWjE9aqd9gAgAAukc2oifGR9yfVcLsNS","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:46 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JAZExJrWfLQtQXxRvYbzD1mS8gM6Hawfo9M5L43s3naNftcBaN3m6YayjKWcyiGygvkzZSLTEfDVKk6Mpt8myKW","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b8f65161-9d4c-4e6f-95df-43ee1068e040\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b8f65161-9d4c-4e6f-95df-43ee1068e040\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4hSZSyyDh8ZS87ok3FqufeSE1q47x7Nqj9uiVYyCmNTsM6GBmdS4x3AwYp5gJE4ByJveNdYtDxfq2RMwArp8GC7t","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"08652f7f-9cc1-494f-a4f2-fe6b16f8262d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"08652f7f-9cc1-494f-a4f2-fe6b16f8262d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5C9JcUPbubhq28716hz52R5pDBqg4aaa6Dk6Bx4XvZf7yEJG2x2xd7zTDoaLMURj9MH3GjssKsxWGWHjLEmNz2VM","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"22VTKmPmpir4r1DhwjdQjivAkSAPYhyMEgynTNaxWLsLtjd9daAZmivr32cDiwGeD1uwZaaB28USU3gekXWGUWS7","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U2GfMiMKYQcGvU6CUFWG68NNp7AyWhTUvqa6p2o2FiXzYQbV4cjpPrq7ZY8gmrKAGTLLmPk1GF1BbY9aZPcVFw5","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"66rB8VoJwYasYUp3Wttbv4jnSmGPKsN7ZFGRfxRxAc8mHpXmcUzuPgCsbbnX4QkneX9NucFnJfmctGF7iXcj2eF7","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4SAJUNvhNaXXuF9mWPtVEqZkWTMYFm9r2u6mwJftSxd5yY7xNAzK5tkN5USoACVwEty3cUYCwNgjD6rAZHonMgpp","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54cSr4h2VMupkSLruxTQWa8CBQJ5K1qN5eKnYhD9vxGik9mMHd966HUtehG5gWjJqE1LrRErTwmMYsYiTKQ96Tn2","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"741bf972-98a9-4cd0-9f06-ae0e44c259b9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"741bf972-98a9-4cd0-9f06-ae0e44c259b9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:28:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U2GfMiMKYQcGvU6CUFWG68NNp7AyWhTUvqa6p2o2FiXzYQbV4cjpPrq7ZY8gmrKAGTLLmPk1GF1BbY9aZPcVFw5","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"22VTKmPmpir4r1DhwjdQjivAkSAPYhyMEgynTNaxWLsLtjd9daAZmivr32cDiwGeD1uwZaaB28USU3gekXWGUWS7","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5C9JcUPbubhq28716hz52R5pDBqg4aaa6Dk6Bx4XvZf7yEJG2x2xd7zTDoaLMURj9MH3GjssKsxWGWHjLEmNz2VM","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:28:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2yU3Af7ZrBrXDdTB7zCHBoM2wsh8Y7JFTxU7FNMCoCchSuTdpQrwSXjr53Uq1jbH8hYhfh7w5dgMCqphBXshdf4f","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:28:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:28:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:28:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U2GfMiMKYQcGvU6CUFWG68NNp7AyWhTUvqa6p2o2FiXzYQbV4cjpPrq7ZY8gmrKAGTLLmPk1GF1BbY9aZPcVFw5","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4hSZSyyDh8ZS87ok3FqufeSE1q47x7Nqj9uiVYyCmNTsM6GBmdS4x3AwYp5gJE4ByJveNdYtDxfq2RMwArp8GC7t","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"53b72af1-6aae-4070-8fe1-54c293cfc513\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"53b72af1-6aae-4070-8fe1-54c293cfc513\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:02 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JAZExJrWfLQtQXxRvYbzD1mS8gM6Hawfo9M5L43s3naNftcBaN3m6YayjKWcyiGygvkzZSLTEfDVKk6Mpt8myKW","metadata":{"attempt":3,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"66rB8VoJwYasYUp3Wttbv4jnSmGPKsN7ZFGRfxRxAc8mHpXmcUzuPgCsbbnX4QkneX9NucFnJfmctGF7iXcj2eF7","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ce6a7ec2-3893-41b4-9908-c8faf914e56b\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ce6a7ec2-3893-41b4-9908-c8faf914e56b\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:03 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:04 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4SAJUNvhNaXXuF9mWPtVEqZkWTMYFm9r2u6mwJftSxd5yY7xNAzK5tkN5USoACVwEty3cUYCwNgjD6rAZHonMgpp","metadata":{"attempt":3,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:06 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"22VTKmPmpir4r1DhwjdQjivAkSAPYhyMEgynTNaxWLsLtjd9daAZmivr32cDiwGeD1uwZaaB28USU3gekXWGUWS7","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"71a89635-014b-43dd-b094-3f426503a2a8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"71a89635-014b-43dd-b094-3f426503a2a8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:06 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:06 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5C9JcUPbubhq28716hz52R5pDBqg4aaa6Dk6Bx4XvZf7yEJG2x2xd7zTDoaLMURj9MH3GjssKsxWGWHjLEmNz2VM","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5bbe58c8-8c2d-4d9c-a9dd-cac8eabe4368\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5bbe58c8-8c2d-4d9c-a9dd-cac8eabe4368\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:06 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"66rB8VoJwYasYUp3Wttbv4jnSmGPKsN7ZFGRfxRxAc8mHpXmcUzuPgCsbbnX4QkneX9NucFnJfmctGF7iXcj2eF7","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4hSZSyyDh8ZS87ok3FqufeSE1q47x7Nqj9uiVYyCmNTsM6GBmdS4x3AwYp5gJE4ByJveNdYtDxfq2RMwArp8GC7t","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2JAZExJrWfLQtQXxRvYbzD1mS8gM6Hawfo9M5L43s3naNftcBaN3m6YayjKWcyiGygvkzZSLTEfDVKk6Mpt8myKW","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:17 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4SAJUNvhNaXXuF9mWPtVEqZkWTMYFm9r2u6mwJftSxd5yY7xNAzK5tkN5USoACVwEty3cUYCwNgjD6rAZHonMgpp","metadata":{"attempt":4,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:42 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:43 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"Drz57uvAL5yWAVcgWnp1g2f6cHFXBgmp76wYo3LzghzvfEzdkoDotT9B6wAxXNqwdkJny1EGSFvkTrHLakbZSyU","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:45 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5swiAwGbfLQ62GgZWCCfbG3c5g3X91vf4RGBxyChb4Wbf7kUQnSHtY2Xsewu6xwK5Cp19HejVWyQ89dgig2n2M2g","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:45 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3wkuHe2UtRtiVc8csd6e3r5qfEykfAeWMtcLuGd114HL5D7MBuQh1DhGGcjHB41eXFSk5FVN5WsoYnhY2TAsTnNc","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:45 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"4RidnJFAKbXt6LFLuXXiZycQKBH3Rpy9sffHiVYV5bW7ap1MaoqKQsWeai7imMHBBfFmXKdkAgMgxVqLgsF4GwEy","metadata":{"attempt":1,"duration":269}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:45 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"4tbV971qFL4G14rRxLCUkFd6AGzubMp6uv6oTj9PUDzetEF8rmZcaswLzAeRPhQN8zjy2tfbQbeoGPrqBifDsSc4","metadata":{"attempt":1,"duration":306}}
2025-05-29 13:29:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:46 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:46 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4BnDy8TwWv69wvXHbiVhsnZdCU3kx2gezfojRYEDvBqPPBoKFX6jnFzDbEoad7J8CudrUXZthmnzxSkCvFmpb2r8","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"05b818ed-7eaf-4315-adef-51128eed0f22\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"05b818ed-7eaf-4315-adef-51128eed0f22\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4qfrgHkkfqUw7Ln3ZX4R8qCkUcrmeZxiX2S8TgvHKrCjzfWfEvDHXRu2QgYtYFSGVWsHsWyQRnku6xcWMgaZtkH9","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"bbb467b0-552e-4cbc-9a8a-3155fc9b1b50\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"bbb467b0-552e-4cbc-9a8a-3155fc9b1b50\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"Drz57uvAL5yWAVcgWnp1g2f6cHFXBgmp76wYo3LzghzvfEzdkoDotT9B6wAxXNqwdkJny1EGSFvkTrHLakbZSyU","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4qfrgHkkfqUw7Ln3ZX4R8qCkUcrmeZxiX2S8TgvHKrCjzfWfEvDHXRu2QgYtYFSGVWsHsWyQRnku6xcWMgaZtkH9","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3wkuHe2UtRtiVc8csd6e3r5qfEykfAeWMtcLuGd114HL5D7MBuQh1DhGGcjHB41eXFSk5FVN5WsoYnhY2TAsTnNc","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"43955981-a2f8-4125-ac97-6a83e9b3d495\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"43955981-a2f8-4125-ac97-6a83e9b3d495\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y7jU6NLrSkubheZge8YSwZHGb195CtetLyoWHAh1uJkBLDarWEoQ4JeS8io9R5nVGim3U111sMVbXcHUdnoffZ7","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"63dR5DzZYkJ91rjZQ5pE13N1UE5SeBnnx1RGwemNhHtG7JrWS2b97AKENSHAqKtA4dM111kPUoWvDNp1CpgnUt6F","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5GXLtoh7qDwweWyFjhcNLDmim984jR8MJG8VvKk5Mvp4qJsFDxkAQS9Y61Gp67ShLHLN3YvFkRAfi1Qgxj1ycZxP","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4RrFpq3cdz9rTGgoiXNwJF49QXeChNKb3UxQJ9Bw3MBioznu1VDZXa4QdhTRW16TB3uqTZfqmHiXX5tLwyoHRwJQ","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4GnuFkCLu4nrPe6KxG9Tz9TBDyn3oGDLHfMcoRzvQArmwUJYZiyC9ZNq8DzM87KYPRXWY6VdFkbT2RKG55jFeGXr","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"57dL8eek9EhDVyiHcEGR78BXXAXXt7Rs7Ua2nDSGpdcC9MPrDoWqq2wNhrtjfJtMmydezxNxBZ627NZ58q93nKg9","metadata":{"attempt":1,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y7jU6NLrSkubheZge8YSwZHGb195CtetLyoWHAh1uJkBLDarWEoQ4JeS8io9R5nVGim3U111sMVbXcHUdnoffZ7","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5GXLtoh7qDwweWyFjhcNLDmim984jR8MJG8VvKk5Mvp4qJsFDxkAQS9Y61Gp67ShLHLN3YvFkRAfi1Qgxj1ycZxP","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"63dR5DzZYkJ91rjZQ5pE13N1UE5SeBnnx1RGwemNhHtG7JrWS2b97AKENSHAqKtA4dM111kPUoWvDNp1CpgnUt6F","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"57dL8eek9EhDVyiHcEGR78BXXAXXt7Rs7Ua2nDSGpdcC9MPrDoWqq2wNhrtjfJtMmydezxNxBZ627NZ58q93nKg9","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5swiAwGbfLQ62GgZWCCfbG3c5g3X91vf4RGBxyChb4Wbf7kUQnSHtY2Xsewu6xwK5Cp19HejVWyQ89dgig2n2M2g","metadata":{"attempt":2,"retries":4},"error":{"message":"fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1675:17)","name":"TypeError"}}
2025-05-29 13:29:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:29:58 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
2025-05-29 13:29:58 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y7jU6NLrSkubheZge8YSwZHGb195CtetLyoWHAh1uJkBLDarWEoQ4JeS8io9R5nVGim3U111sMVbXcHUdnoffZ7","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:29:59 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:59 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:59 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:29:59 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":3}}
2025-05-29 13:30:00 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:30:00 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":3}}
2025-05-29 13:30:16 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":2,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana"]}}
2025-05-29 13:30:17 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:30:17 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:30:17 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"bigboss1","userCount":1}}
2025-05-29 13:30:17 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GdGPXKEyHUhHFJ187tsQrexBM1EUWwUBT2a32FuDD1DP5W6MfnacHGobp9kxJEd7e4W7GAnVZWNVz1sdzqCDptJ","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:47 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3LJ67caPx1mGyMbsLg58HjMocRZWjSahHajuk1yCXPbBTEXWHpRLF9XsUV8fgUTTmoucagezXJMonJdHviQXmv44","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:47 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2HzyFEZ3FAUkUV3uUiGBiexYJomcL8RvfVSEC5kHgfp3QQ5VWRTenwbkWXMpT62k6qwAwA1xyeVHJxA5rMdHb2oi","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:47 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"jKE6WFP9rmo9UNFmC6AvFFmd1PVVS3e5qFzzxVAJHWT5KFVfRG6s8mgjJoxLopgc8LADzrULQky99N5ouvLQ6ws","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:47 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3bcLAbF9PR2goj5rAp7fQpL8w1aSr1FRmhnFf7Nb8jQs9iAuthoCfNBZ6JnttUns4aXwjtbVfacnhxBLx9TgzFEA","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3g5bTTz2VAtvnjQsgrbwD5sirEdrrpckPH5Rg3EwSjYUNWWYaC6Hnyzgbrmf5SLevKeiqjkNPPPivFivrEv84eTf","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:48 DEBUG [transaction-fetcher] No transaction details found for JLYv41XSW5yQG1wfC9FEmKNMZVCarLH1Wr1qAgXXYwpwp9yxshZ4hijSpNhmqtBsVBkXtNU6ZVmiHLHPSZyHFkL {"transactionSignature":"JLYv41XSW5yQG1wfC9FEmKNMZVCarLH1Wr1qAgXXYwpwp9yxshZ4hijSpNhmqtBsVBkXtNU6ZVmiHLHPSZyHFkL","metadata":{"attempt":1,"retries":4}}
2025-05-29 13:30:48 DEBUG [transaction-fetcher] No transaction details found for 491YW8MX9SzzR68ztzPWm26XieubvwpJkv8mdXSs5pLek4gD14YudmjTmArJj4M65CUmoesuiQMY5vbLUmkRBCEL {"transactionSignature":"491YW8MX9SzzR68ztzPWm26XieubvwpJkv8mdXSs5pLek4gD14YudmjTmArJj4M65CUmoesuiQMY5vbLUmkRBCEL","metadata":{"attempt":1,"retries":4}}
2025-05-29 13:30:49 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:49 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:49 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:49 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:49 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:50 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2HzyFEZ3FAUkUV3uUiGBiexYJomcL8RvfVSEC5kHgfp3QQ5VWRTenwbkWXMpT62k6qwAwA1xyeVHJxA5rMdHb2oi","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3bcLAbF9PR2goj5rAp7fQpL8w1aSr1FRmhnFf7Nb8jQs9iAuthoCfNBZ6JnttUns4aXwjtbVfacnhxBLx9TgzFEA","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:50 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"JLYv41XSW5yQG1wfC9FEmKNMZVCarLH1Wr1qAgXXYwpwp9yxshZ4hijSpNhmqtBsVBkXtNU6ZVmiHLHPSZyHFkL","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:50 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:30:54 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"JLYv41XSW5yQG1wfC9FEmKNMZVCarLH1Wr1qAgXXYwpwp9yxshZ4hijSpNhmqtBsVBkXtNU6ZVmiHLHPSZyHFkL","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:30:59 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"55KheR331kaBryLCPFFQZvpABYckNAiGBrb1G72PzCjz379SNeX4SxT2jQd4XMouB1Sw6Si86q29gL7pQ7iyndbW","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ba3a8716-3eee-465e-b6cf-4130812ed5c9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ba3a8716-3eee-465e-b6cf-4130812ed5c9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:59 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qtefPdZF5dYHM2MMUXBBVh6ZdyWyAckDbvYVXm79gsxswQ9pbGyFgFsanN8JCLE3TK6QRrnubWhkfJbmMskjMWg","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e4cd06c3-029e-4204-b41d-95f79d8f2675\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e4cd06c3-029e-4204-b41d-95f79d8f2675\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:59 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kKGLwpUYaLUj8Dhibz5Pvuyq5PzXfMFzg2P7tCEiKf5MvUkWvPu83yRs8uEna6oyQ2aNBGnxrmftg7stQL3NdLV","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2eb77a9a-befe-4f0b-8d46-52d42d6d42ec\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2eb77a9a-befe-4f0b-8d46-52d42d6d42ec\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:30:59 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2xcYKVhQotL73Lda8GXiRsTnF2NG6GPzsQPBMM9TBBeGvzu7w8NGT6PErHHBPhjehvtH8xPuG2jVeqVYpdrYXshx","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"27899a99-0662-4cf5-9121-24f311a8f45e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"27899a99-0662-4cf5-9121-24f311a8f45e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qtefPdZF5dYHM2MMUXBBVh6ZdyWyAckDbvYVXm79gsxswQ9pbGyFgFsanN8JCLE3TK6QRrnubWhkfJbmMskjMWg","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kKGLwpUYaLUj8Dhibz5Pvuyq5PzXfMFzg2P7tCEiKf5MvUkWvPu83yRs8uEna6oyQ2aNBGnxrmftg7stQL3NdLV","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"jKE6WFP9rmo9UNFmC6AvFFmd1PVVS3e5qFzzxVAJHWT5KFVfRG6s8mgjJoxLopgc8LADzrULQky99N5ouvLQ6ws","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8b6905ba-d842-4af6-8440-5bf188d1b51a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8b6905ba-d842-4af6-8440-5bf188d1b51a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3g5bTTz2VAtvnjQsgrbwD5sirEdrrpckPH5Rg3EwSjYUNWWYaC6Hnyzgbrmf5SLevKeiqjkNPPPivFivrEv84eTf","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4d58f3ef-db83-4f37-82a4-689126cc0b5c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4d58f3ef-db83-4f37-82a4-689126cc0b5c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3LJ67caPx1mGyMbsLg58HjMocRZWjSahHajuk1yCXPbBTEXWHpRLF9XsUV8fgUTTmoucagezXJMonJdHviQXmv44","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f51de0c7-da8d-4991-b4f5-3d517207c80d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f51de0c7-da8d-4991-b4f5-3d517207c80d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GdGPXKEyHUhHFJ187tsQrexBM1EUWwUBT2a32FuDD1DP5W6MfnacHGobp9kxJEd7e4W7GAnVZWNVz1sdzqCDptJ","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"11b3bda6-bbc0-4942-b51f-1ac67f157a2d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"11b3bda6-bbc0-4942-b51f-1ac67f157a2d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:02 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"491YW8MX9SzzR68ztzPWm26XieubvwpJkv8mdXSs5pLek4gD14YudmjTmArJj4M65CUmoesuiQMY5vbLUmkRBCEL","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"eb9d786d-037f-4bbb-81ba-8ecad33521dc\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"eb9d786d-037f-4bbb-81ba-8ecad33521dc\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:02 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:05 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:05 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3bcLAbF9PR2goj5rAp7fQpL8w1aSr1FRmhnFf7Nb8jQs9iAuthoCfNBZ6JnttUns4aXwjtbVfacnhxBLx9TgzFEA","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ccfeb531-654d-4ca7-864b-bf3a7325a545\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ccfeb531-654d-4ca7-864b-bf3a7325a545\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:05 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2HzyFEZ3FAUkUV3uUiGBiexYJomcL8RvfVSEC5kHgfp3QQ5VWRTenwbkWXMpT62k6qwAwA1xyeVHJxA5rMdHb2oi","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0f11885f-c471-42be-bac4-a76f1f616090\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0f11885f-c471-42be-bac4-a76f1f616090\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:05 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"jKE6WFP9rmo9UNFmC6AvFFmd1PVVS3e5qFzzxVAJHWT5KFVfRG6s8mgjJoxLopgc8LADzrULQky99N5ouvLQ6ws","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:05 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3LJ67caPx1mGyMbsLg58HjMocRZWjSahHajuk1yCXPbBTEXWHpRLF9XsUV8fgUTTmoucagezXJMonJdHviQXmv44","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:05 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3g5bTTz2VAtvnjQsgrbwD5sirEdrrpckPH5Rg3EwSjYUNWWYaC6Hnyzgbrmf5SLevKeiqjkNPPPivFivrEv84eTf","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:06 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hW5VCYkez98zYEStXSqBcuy1QqmP18iNrevB6jfaRBWTtmAM9u3uhrogff6wwnr8nXPGDug9hT2GYHJoWCWoL6P","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"31586bca-9cbe-40b6-b48e-631e902df2fb\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"31586bca-9cbe-40b6-b48e-631e902df2fb\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hW5VCYkez98zYEStXSqBcuy1QqmP18iNrevB6jfaRBWTtmAM9u3uhrogff6wwnr8nXPGDug9hT2GYHJoWCWoL6P","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:12 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"55KheR331kaBryLCPFFQZvpABYckNAiGBrb1G72PzCjz379SNeX4SxT2jQd4XMouB1Sw6Si86q29gL7pQ7iyndbW","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f59c2c7-d344-4b1d-a13b-0b881b931f7e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f59c2c7-d344-4b1d-a13b-0b881b931f7e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2xcYKVhQotL73Lda8GXiRsTnF2NG6GPzsQPBMM9TBBeGvzu7w8NGT6PErHHBPhjehvtH8xPuG2jVeqVYpdrYXshx","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f751866-c66e-46d4-9714-446cb1b03b8a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f751866-c66e-46d4-9714-446cb1b03b8a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3bcLAbF9PR2goj5rAp7fQpL8w1aSr1FRmhnFf7Nb8jQs9iAuthoCfNBZ6JnttUns4aXwjtbVfacnhxBLx9TgzFEA","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:14 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3g5bTTz2VAtvnjQsgrbwD5sirEdrrpckPH5Rg3EwSjYUNWWYaC6Hnyzgbrmf5SLevKeiqjkNPPPivFivrEv84eTf","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:14 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:14 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hW5VCYkez98zYEStXSqBcuy1QqmP18iNrevB6jfaRBWTtmAM9u3uhrogff6wwnr8nXPGDug9hT2GYHJoWCWoL6P","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:14 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"JLYv41XSW5yQG1wfC9FEmKNMZVCarLH1Wr1qAgXXYwpwp9yxshZ4hijSpNhmqtBsVBkXtNU6ZVmiHLHPSZyHFkL","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6b614ff4-1e43-4ea0-8414-3ef286436aea\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6b614ff4-1e43-4ea0-8414-3ef286436aea\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:16 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qtefPdZF5dYHM2MMUXBBVh6ZdyWyAckDbvYVXm79gsxswQ9pbGyFgFsanN8JCLE3TK6QRrnubWhkfJbmMskjMWg","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ec0519b4-5d7f-4334-b4dc-1445d4514c49\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ec0519b4-5d7f-4334-b4dc-1445d4514c49\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kKGLwpUYaLUj8Dhibz5Pvuyq5PzXfMFzg2P7tCEiKf5MvUkWvPu83yRs8uEna6oyQ2aNBGnxrmftg7stQL3NdLV","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0dd02173-00b1-4572-8eb9-fe84e6c80060\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0dd02173-00b1-4572-8eb9-fe84e6c80060\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:17 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:17 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GdGPXKEyHUhHFJ187tsQrexBM1EUWwUBT2a32FuDD1DP5W6MfnacHGobp9kxJEd7e4W7GAnVZWNVz1sdzqCDptJ","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1e5a19ee-aa55-445f-b7de-1fc3ef57911e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1e5a19ee-aa55-445f-b7de-1fc3ef57911e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"491YW8MX9SzzR68ztzPWm26XieubvwpJkv8mdXSs5pLek4gD14YudmjTmArJj4M65CUmoesuiQMY5vbLUmkRBCEL","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c4bb5999-0115-4733-8a20-311f95f82df7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c4bb5999-0115-4733-8a20-311f95f82df7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:22 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:25 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:25 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2HzyFEZ3FAUkUV3uUiGBiexYJomcL8RvfVSEC5kHgfp3QQ5VWRTenwbkWXMpT62k6qwAwA1xyeVHJxA5rMdHb2oi","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c82335c-97e3-4b31-898d-78cf14a63818\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c82335c-97e3-4b31-898d-78cf14a63818\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"jKE6WFP9rmo9UNFmC6AvFFmd1PVVS3e5qFzzxVAJHWT5KFVfRG6s8mgjJoxLopgc8LADzrULQky99N5ouvLQ6ws","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"80ce4707-8f8b-48db-a28f-f70722cd95d9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"80ce4707-8f8b-48db-a28f-f70722cd95d9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3LJ67caPx1mGyMbsLg58HjMocRZWjSahHajuk1yCXPbBTEXWHpRLF9XsUV8fgUTTmoucagezXJMonJdHviQXmv44","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c546c289-35c2-4b53-a053-0e7f84278a40\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c546c289-35c2-4b53-a053-0e7f84278a40\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:26 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GdGPXKEyHUhHFJ187tsQrexBM1EUWwUBT2a32FuDD1DP5W6MfnacHGobp9kxJEd7e4W7GAnVZWNVz1sdzqCDptJ","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:26 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:26 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"491YW8MX9SzzR68ztzPWm26XieubvwpJkv8mdXSs5pLek4gD14YudmjTmArJj4M65CUmoesuiQMY5vbLUmkRBCEL","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:28 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"55KheR331kaBryLCPFFQZvpABYckNAiGBrb1G72PzCjz379SNeX4SxT2jQd4XMouB1Sw6Si86q29gL7pQ7iyndbW","metadata":{"attempt":3,"duration":11384}}
2025-05-29 13:31:28 WARN [transaction-fetcher] getParsedTransaction slow (40632ms) {"duration":40632,"metadata":{"isSlowOperation":true}}
2025-05-29 13:31:28 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:28 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"2xcYKVhQotL73Lda8GXiRsTnF2NG6GPzsQPBMM9TBBeGvzu7w8NGT6PErHHBPhjehvtH8xPuG2jVeqVYpdrYXshx","metadata":{"attempt":3,"duration":11255}}
2025-05-29 13:31:28 WARN [transaction-fetcher] getParsedTransaction slow (40709ms) {"duration":40709,"metadata":{"isSlowOperation":true}}
2025-05-29 13:31:28 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:31 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3DftipbAzazr6cgL5oZoBD3RyvUmPFNWCyomNoSvZ3s8H1beSfnk71rG6CTSJtK9giaSZ1z9CtBCpL1Wfeikr8uJ","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:32 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hW5VCYkez98zYEStXSqBcuy1QqmP18iNrevB6jfaRBWTtmAM9u3uhrogff6wwnr8nXPGDug9hT2GYHJoWCWoL6P","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2c63eec7-1b89-4ee3-9474-8bdcb718cf75\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2c63eec7-1b89-4ee3-9474-8bdcb718cf75\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:34 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:34 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3DftipbAzazr6cgL5oZoBD3RyvUmPFNWCyomNoSvZ3s8H1beSfnk71rG6CTSJtK9giaSZ1z9CtBCpL1Wfeikr8uJ","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:35 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qtefPdZF5dYHM2MMUXBBVh6ZdyWyAckDbvYVXm79gsxswQ9pbGyFgFsanN8JCLE3TK6QRrnubWhkfJbmMskjMWg","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3ca53112-ac33-4037-9d78-d1b53199866b\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3ca53112-ac33-4037-9d78-d1b53199866b\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:35 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kKGLwpUYaLUj8Dhibz5Pvuyq5PzXfMFzg2P7tCEiKf5MvUkWvPu83yRs8uEna6oyQ2aNBGnxrmftg7stQL3NdLV","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ee333829-c719-475e-9980-1dadb04a9e42\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ee333829-c719-475e-9980-1dadb04a9e42\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:38 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":2}}
2025-05-29 13:31:42 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"276AqvcPJsERVuvwEKX9SS6feDaAPV9vkESiCmjUnc53CbY3gHSujHfQ9xbzVX7W1yrQAgacp6QsGNY4Gf9Rk5wA","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2f8de9f0-d72c-4d34-b72d-c9e59608bf89\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2f8de9f0-d72c-4d34-b72d-c9e59608bf89\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:31:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":2}}
2025-05-29 13:31:44 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"276AqvcPJsERVuvwEKX9SS6feDaAPV9vkESiCmjUnc53CbY3gHSujHfQ9xbzVX7W1yrQAgacp6QsGNY4Gf9Rk5wA","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:43:29 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":1,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com"]}}
2025-05-29 13:43:31 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:43:31 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:50:19 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userCount":1}}
2025-05-29 13:50:19 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"58bZTN6VYUQB3zgE5m6VMU9tLdWrKS5AXYNRDp3y6bEM7jZqrqU2Am7xJ3tfgeSfFVP9Ve7LGi7D2DtAGyypPGTZ","metadata":{"attempt":1,"duration":369}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:29 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"Rfbn4wNb3XamdRhFwzmceWwMknZ2Rc6UcnT8W5bfDKzz8mb418gmsKTzrBCWgbCTTrauJgKj2FJ2k2TWA1oWfaN","metadata":{"attempt":1,"duration":389}}
2025-05-29 13:50:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4oCTrmJKLrKC2Du3hKZJL259ARFaBuYT1eMSiXvFTkZttj6HyCwTJiMbzcmP2MWFccwuBv5SBs95TWE4MDMyFiJs","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"75e7330b-51ae-4980-8e92-71f986b8d310\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"75e7330b-51ae-4980-8e92-71f986b8d310\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"opB2CsdB3rVa6z41aySP1jqZiKWbQM6s2Fy8WmcvqcdnXdpXknWNAiqdriFeatRgoB5RQp9VmrCJiRbAMvs6c3Y","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e7578111-f36c-43de-8e4f-912d69a2c553\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e7578111-f36c-43de-8e4f-912d69a2c553\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U1fTZnu7RDLjQKtJPuhH2qFecUvmmik5Jo1YjmiGi6XiWie76ShGrfk84FfcfX78ZTpWt9ybK7vsDfVibY9iKrc","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8730ac16-0299-4510-8c8a-0c814462505f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8730ac16-0299-4510-8c8a-0c814462505f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4BEqJW9bh8zbJr2oUpaqEeFTkFjKESu55rxkg2xBovtzJ7Gu3BSVwuabHcvsfEg79Mu8rYH8WxHjUuqBoFcYp7Vq","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e6ca1348-d4d2-42d2-a367-79903cf17f66\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e6ca1348-d4d2-42d2-a367-79903cf17f66\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2gaYqux7YHQ8zs3ETQ27fguN36A417ii9fRpqnUefwW4R9rvMSBKokydCbPHhxguq9eFBBPteYZ1uzZq6t3aNxie","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9e06d3de-72c5-47cc-ad52-a10e2898e62e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9e06d3de-72c5-47cc-ad52-a10e2898e62e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"XfTSL4coAyifqRacezhQmJn1y6TFaGg7otc7cMZkZXWE3E4XT455RYZfUTdvbznmfdTj6eNcPPvVcu2DLqisMr1","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4ca611f3-c6d9-45ce-9632-daa8bf602f9c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4ca611f3-c6d9-45ce-9632-daa8bf602f9c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3HMN4udyrNvGQ8cu4t6zNhzMxngERk7yFmVfkzFrfk9w9vbjJa7ajgwEyDDPZuBsZM7yUULUEnX1NPEjcJF3j4Bb","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2d524865-bd3a-42b2-a462-cbdad0990b8f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2d524865-bd3a-42b2-a462-cbdad0990b8f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2THcQ7dYNna3qg5FgaZKvYT5b3ppX4KPL6VPrLEedViCRkzBkNN8iAixGwWzgwtnMkCwQQhsHAVZrt8EUxiSvCDE","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"650df5ec-c9ad-40ee-b21d-bd4eb59c0029\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"650df5ec-c9ad-40ee-b21d-bd4eb59c0029\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hMUJiCEmiJuag15pCAjQ9FnUWXkKynpuAbkUxunhnYnGGPhJWzpXk5G977izCvb1WG7L2CFB11HHUD5SNNNn4du","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d72b3210-a8a1-4767-bee0-98b75b8dc4ed\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d72b3210-a8a1-4767-bee0-98b75b8dc4ed\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:37 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"284gqAYN7wRb9EGAm9AyFbZQaTgReX6zyMQcr7xHx6q2rDcQ7RnPe5rTemo2sYn4vD1owrbf1kpr7NmjhGj4RLZk","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a03fa80a-1e13-4441-b109-f313d004ec6c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a03fa80a-1e13-4441-b109-f313d004ec6c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4oCTrmJKLrKC2Du3hKZJL259ARFaBuYT1eMSiXvFTkZttj6HyCwTJiMbzcmP2MWFccwuBv5SBs95TWE4MDMyFiJs","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b41b2403-1fa1-4501-8cf3-43db3f5bac99\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b41b2403-1fa1-4501-8cf3-43db3f5bac99\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"opB2CsdB3rVa6z41aySP1jqZiKWbQM6s2Fy8WmcvqcdnXdpXknWNAiqdriFeatRgoB5RQp9VmrCJiRbAMvs6c3Y","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c89e42bb-bd72-489b-8620-adee66188566\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c89e42bb-bd72-489b-8620-adee66188566\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4BEqJW9bh8zbJr2oUpaqEeFTkFjKESu55rxkg2xBovtzJ7Gu3BSVwuabHcvsfEg79Mu8rYH8WxHjUuqBoFcYp7Vq","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f261e41-c8f5-40dc-9d1b-42aacdcebcec\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f261e41-c8f5-40dc-9d1b-42aacdcebcec\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"XfTSL4coAyifqRacezhQmJn1y6TFaGg7otc7cMZkZXWE3E4XT455RYZfUTdvbznmfdTj6eNcPPvVcu2DLqisMr1","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8550e6ef-0338-4dd6-b0f1-a34889d8f1a7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8550e6ef-0338-4dd6-b0f1-a34889d8f1a7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U1fTZnu7RDLjQKtJPuhH2qFecUvmmik5Jo1YjmiGi6XiWie76ShGrfk84FfcfX78ZTpWt9ybK7vsDfVibY9iKrc","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"bff2cfac-3d89-457a-9a23-f7adaee2ce70\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"bff2cfac-3d89-457a-9a23-f7adaee2ce70\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2gaYqux7YHQ8zs3ETQ27fguN36A417ii9fRpqnUefwW4R9rvMSBKokydCbPHhxguq9eFBBPteYZ1uzZq6t3aNxie","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"682a765a-443d-409e-8bb3-9295f327130d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"682a765a-443d-409e-8bb3-9295f327130d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2THcQ7dYNna3qg5FgaZKvYT5b3ppX4KPL6VPrLEedViCRkzBkNN8iAixGwWzgwtnMkCwQQhsHAVZrt8EUxiSvCDE","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"df3f133c-9fda-4f67-9602-d84f15e9db85\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"df3f133c-9fda-4f67-9602-d84f15e9db85\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"284gqAYN7wRb9EGAm9AyFbZQaTgReX6zyMQcr7xHx6q2rDcQ7RnPe5rTemo2sYn4vD1owrbf1kpr7NmjhGj4RLZk","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"28147c32-fae4-499c-bda5-fbe2ea3af39d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"28147c32-fae4-499c-bda5-fbe2ea3af39d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3HMN4udyrNvGQ8cu4t6zNhzMxngERk7yFmVfkzFrfk9w9vbjJa7ajgwEyDDPZuBsZM7yUULUEnX1NPEjcJF3j4Bb","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"15be5484-ab90-4846-92ec-638e6da4a09b\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"15be5484-ab90-4846-92ec-638e6da4a09b\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hMUJiCEmiJuag15pCAjQ9FnUWXkKynpuAbkUxunhnYnGGPhJWzpXk5G977izCvb1WG7L2CFB11HHUD5SNNNn4du","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"07a53c69-abe4-44e7-a95a-b104ade1766c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"07a53c69-abe4-44e7-a95a-b104ade1766c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4oCTrmJKLrKC2Du3hKZJL259ARFaBuYT1eMSiXvFTkZttj6HyCwTJiMbzcmP2MWFccwuBv5SBs95TWE4MDMyFiJs","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7a6d398d-025b-456f-87d2-89f23b5b6215\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7a6d398d-025b-456f-87d2-89f23b5b6215\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"opB2CsdB3rVa6z41aySP1jqZiKWbQM6s2Fy8WmcvqcdnXdpXknWNAiqdriFeatRgoB5RQp9VmrCJiRbAMvs6c3Y","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41b393b0-2ef3-4567-9e84-7f4a35560e52\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41b393b0-2ef3-4567-9e84-7f4a35560e52\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4BEqJW9bh8zbJr2oUpaqEeFTkFjKESu55rxkg2xBovtzJ7Gu3BSVwuabHcvsfEg79Mu8rYH8WxHjUuqBoFcYp7Vq","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"757e53c8-6a57-48d6-a123-a81c8d8b324c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"757e53c8-6a57-48d6-a123-a81c8d8b324c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U1fTZnu7RDLjQKtJPuhH2qFecUvmmik5Jo1YjmiGi6XiWie76ShGrfk84FfcfX78ZTpWt9ybK7vsDfVibY9iKrc","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"602b8f3d-5560-4a87-a85e-f69be76fe179\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"602b8f3d-5560-4a87-a85e-f69be76fe179\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"XfTSL4coAyifqRacezhQmJn1y6TFaGg7otc7cMZkZXWE3E4XT455RYZfUTdvbznmfdTj6eNcPPvVcu2DLqisMr1","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8eaeada2-37be-46f4-b100-f98d17158416\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8eaeada2-37be-46f4-b100-f98d17158416\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2gaYqux7YHQ8zs3ETQ27fguN36A417ii9fRpqnUefwW4R9rvMSBKokydCbPHhxguq9eFBBPteYZ1uzZq6t3aNxie","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c765a279-adcc-4ecd-b3eb-578f7bc3784e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c765a279-adcc-4ecd-b3eb-578f7bc3784e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2THcQ7dYNna3qg5FgaZKvYT5b3ppX4KPL6VPrLEedViCRkzBkNN8iAixGwWzgwtnMkCwQQhsHAVZrt8EUxiSvCDE","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dc4b91e3-2874-4554-9a43-4e9084450d0d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dc4b91e3-2874-4554-9a43-4e9084450d0d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3HMN4udyrNvGQ8cu4t6zNhzMxngERk7yFmVfkzFrfk9w9vbjJa7ajgwEyDDPZuBsZM7yUULUEnX1NPEjcJF3j4Bb","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"299d6ecf-567a-490d-9d4b-1eabdb84a441\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"299d6ecf-567a-490d-9d4b-1eabdb84a441\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"284gqAYN7wRb9EGAm9AyFbZQaTgReX6zyMQcr7xHx6q2rDcQ7RnPe5rTemo2sYn4vD1owrbf1kpr7NmjhGj4RLZk","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"863df4bf-60ce-45a3-a501-764502d479d2\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"863df4bf-60ce-45a3-a501-764502d479d2\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hMUJiCEmiJuag15pCAjQ9FnUWXkKynpuAbkUxunhnYnGGPhJWzpXk5G977izCvb1WG7L2CFB11HHUD5SNNNn4du","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8600255b-5b01-4948-82af-e4a8d280882a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8600255b-5b01-4948-82af-e4a8d280882a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:09 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"4oCTrmJKLrKC2Du3hKZJL259ARFaBuYT1eMSiXvFTkZttj6HyCwTJiMbzcmP2MWFccwuBv5SBs95TWE4MDMyFiJs","metadata":{"attempt":4,"duration":488}}
2025-05-29 13:51:09 WARN [transaction-fetcher] getParsedTransaction slow (40098ms) {"duration":40098,"metadata":{"isSlowOperation":true}}
2025-05-29 13:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:09 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"opB2CsdB3rVa6z41aySP1jqZiKWbQM6s2Fy8WmcvqcdnXdpXknWNAiqdriFeatRgoB5RQp9VmrCJiRbAMvs6c3Y","metadata":{"attempt":4,"duration":512}}
2025-05-29 13:51:09 WARN [transaction-fetcher] getParsedTransaction slow (40196ms) {"duration":40196,"metadata":{"isSlowOperation":true}}
2025-05-29 13:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 13:51:17 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2gaYqux7YHQ8zs3ETQ27fguN36A417ii9fRpqnUefwW4R9rvMSBKokydCbPHhxguq9eFBBPteYZ1uzZq6t3aNxie","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7f67187b-d52b-4c3f-8181-2ebe204e01a6\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7f67187b-d52b-4c3f-8181-2ebe204e01a6\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:17 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"XfTSL4coAyifqRacezhQmJn1y6TFaGg7otc7cMZkZXWE3E4XT455RYZfUTdvbznmfdTj6eNcPPvVcu2DLqisMr1","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5286cda5-a687-487f-bacf-6fc0e7738ab5\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5286cda5-a687-487f-bacf-6fc0e7738ab5\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2THcQ7dYNna3qg5FgaZKvYT5b3ppX4KPL6VPrLEedViCRkzBkNN8iAixGwWzgwtnMkCwQQhsHAVZrt8EUxiSvCDE","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"61be53d7-fb15-4001-b0ed-ac80fdbf483e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"61be53d7-fb15-4001-b0ed-ac80fdbf483e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3HMN4udyrNvGQ8cu4t6zNhzMxngERk7yFmVfkzFrfk9w9vbjJa7ajgwEyDDPZuBsZM7yUULUEnX1NPEjcJF3j4Bb","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0c19d49f-e8c6-41b7-96c6-7fd6f707c2f3\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0c19d49f-e8c6-41b7-96c6-7fd6f707c2f3\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3hMUJiCEmiJuag15pCAjQ9FnUWXkKynpuAbkUxunhnYnGGPhJWzpXk5G977izCvb1WG7L2CFB11HHUD5SNNNn4du","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"762363c2-aa0f-471d-9eb2-86faf756d6c9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"762363c2-aa0f-471d-9eb2-86faf756d6c9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4U1fTZnu7RDLjQKtJPuhH2qFecUvmmik5Jo1YjmiGi6XiWie76ShGrfk84FfcfX78ZTpWt9ybK7vsDfVibY9iKrc","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ea816fe3-0e1e-4ce5-a352-e52ef1eb5e34\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ea816fe3-0e1e-4ce5-a352-e52ef1eb5e34\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"284gqAYN7wRb9EGAm9AyFbZQaTgReX6zyMQcr7xHx6q2rDcQ7RnPe5rTemo2sYn4vD1owrbf1kpr7NmjhGj4RLZk","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6f7f8bf6-3cdf-4d3e-85fe-d574202b6287\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6f7f8bf6-3cdf-4d3e-85fe-d574202b6287\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 13:51:18 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4BEqJW9bh8zbJr2oUpaqEeFTkFjKESu55rxkg2xBovtzJ7Gu3BSVwuabHcvsfEg79Mu8rYH8WxHjUuqBoFcYp7Vq","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b2c226f4-4f49-43dc-8ee4-eb53d3024922\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b2c226f4-4f49-43dc-8ee4-eb53d3024922\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:14:02 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":1,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com"]}}
2025-05-29 14:14:03 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 14:14:03 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 14:21:09 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":1,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com"]}}
2025-05-29 14:21:10 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 14:21:10 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 14:24:25 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userCount":1}}
2025-05-29 14:24:25 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 14:24:42 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:43 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"53LEHDbeDh71bwLqfjAbgcRtqMEUTzugbgoxKq8tzZPx5EBUNFjKTW1vaawNKURo1vhjhxQUvCQCZTfyi1mQGZMy","metadata":{"attempt":1,"duration":995}}
2025-05-29 14:24:43 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:45 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"4GnJ6JDoBciNAJ5rDjrwFBbVQPPJRf6ZAk4TZGTejh8XsoqdeVWqfDPaYWJdbhkACQpgWbUB2SsBdrUxJrvSKDh9","metadata":{"attempt":1,"duration":943}}
2025-05-29 14:24:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4iibwkmGHPJapkMEFhTXcxkkMdXyycDt7puQQtuzBsexhTgSkHYAmSRpnUGrMMuJ2H7aed3o19KMGwKpcgFsYyn6","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"14cc6fde-7f93-4dc3-b619-aae69656d25f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"14cc6fde-7f93-4dc3-b619-aae69656d25f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:24:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3L5hJ5UrDew1LS6oQthPod5JRfKzzfzaipeBjYe4FYNDSNaqNkhnpQoYp2d6pMJhK5NrYbVjHWTn3XUMqc48uzHx","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"51b814cc-cda7-48e8-9f94-5ebbff66b733\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"51b814cc-cda7-48e8-9f94-5ebbff66b733\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:24:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:24:58 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:06 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"9DhWHMfXwU3q9z2B86aF8DSTLqpffWW4q54sSe7fcFsAkNec4evmBTBT8wQpAZrTvvPDAHuGQKHKJuCCLxoHnWa","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"906fc3da-42dd-4c5b-9991-6b11862eeeb0\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"906fc3da-42dd-4c5b-9991-6b11862eeeb0\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"1tHx6ip3hV8QQhdGoMYcPMAxpug7fwPRZQ4w95BjMVGKd5E2PrGZzNsKBZoGTRahfmA5GA87JmqeHCEmfT478Pp","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ed62808b-b69a-4cf7-a129-6c496e210a05\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ed62808b-b69a-4cf7-a129-6c496e210a05\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GE6jpw5vDECxpKpb8GtqFYDQBGHDgcE4F3zMPdCB11LLnvjL4w6wHWqnBhW8GHesYfQnN6EjC7C8tmwHwaY4wTB","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0fb9539b-7894-4c66-a700-489f5028d33c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0fb9539b-7894-4c66-a700-489f5028d33c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3c3VPMMD2GNBSjN8o2oHdASEGKCDGZefdYoCMQmGP5v7HRyXxhrBxN6yyoJsn2RERiMQCpshd5p6gJ2VUVpMpWoN","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"93f3854c-a7e8-45b7-bdc1-8ebd9b7e79a7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"93f3854c-a7e8-45b7-bdc1-8ebd9b7e79a7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5M3G1L5kHhurzXC1tU6xj61YHBTxJTFyCPGUkSU5NXt5QCRqQCAfxN5hhpaNYgm5YZtuKwXox1cqS5Z9ifE8HkZ","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1d8c9353-1282-4b3c-bdde-3ab940f28a5a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1d8c9353-1282-4b3c-bdde-3ab940f28a5a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Tgd4akFw5CPDUnWdQ5z8jfVXGtQm5BMCfRba6oiKBn15cdKaf5gYfGAwWEWAv4WqSopgGMqaYE9UZBW7i3HLmG2","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"caed8af8-d44f-49a6-8a9e-aa54aeac829a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"caed8af8-d44f-49a6-8a9e-aa54aeac829a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4qunvLaW2jXqMyFLHDcfKHtiMfjHvG3HivSaqtoXbZGBV9DJMTmW5794nUC3ycpvY1HYG5TW6t9t4x5jZXMF8hBt","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e8d90f42-89ab-43a5-bbd6-e358a1071678\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e8d90f42-89ab-43a5-bbd6-e358a1071678\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:07 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Xey44bx8ZxxMTh9U7qwUAG2jTSaHhkcD2EL93EnsS3XEhLahtDiCfyHYHxNtv4uRAaG6fXucbJTxAqZio1bvM4T","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"270833fc-109a-4ffb-bd96-5053e6129f8c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"270833fc-109a-4ffb-bd96-5053e6129f8c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4iibwkmGHPJapkMEFhTXcxkkMdXyycDt7puQQtuzBsexhTgSkHYAmSRpnUGrMMuJ2H7aed3o19KMGwKpcgFsYyn6","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"82917327-1a59-4ec6-853d-a207922ecc72\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"82917327-1a59-4ec6-853d-a207922ecc72\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3L5hJ5UrDew1LS6oQthPod5JRfKzzfzaipeBjYe4FYNDSNaqNkhnpQoYp2d6pMJhK5NrYbVjHWTn3XUMqc48uzHx","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0d2f6da0-1d31-4c3c-9542-988baa408e95\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0d2f6da0-1d31-4c3c-9542-988baa408e95\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:14 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:15 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"9DhWHMfXwU3q9z2B86aF8DSTLqpffWW4q54sSe7fcFsAkNec4evmBTBT8wQpAZrTvvPDAHuGQKHKJuCCLxoHnWa","metadata":{"attempt":2,"duration":6417}}
2025-05-29 14:25:15 WARN [transaction-fetcher] getParsedTransaction slow (20776ms) {"duration":20776,"metadata":{"isSlowOperation":true}}
2025-05-29 14:25:15 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:15 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"5M3G1L5kHhurzXC1tU6xj61YHBTxJTFyCPGUkSU5NXt5QCRqQCAfxN5hhpaNYgm5YZtuKwXox1cqS5Z9ifE8HkZ","metadata":{"attempt":2,"duration":6374}}
2025-05-29 14:25:15 WARN [transaction-fetcher] getParsedTransaction slow (20288ms) {"duration":20288,"metadata":{"isSlowOperation":true}}
2025-05-29 14:25:15 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GE6jpw5vDECxpKpb8GtqFYDQBGHDgcE4F3zMPdCB11LLnvjL4w6wHWqnBhW8GHesYfQnN6EjC7C8tmwHwaY4wTB","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"38250d1a-ad77-431a-a5ea-f65f0add4223\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"38250d1a-ad77-431a-a5ea-f65f0add4223\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"1tHx6ip3hV8QQhdGoMYcPMAxpug7fwPRZQ4w95BjMVGKd5E2PrGZzNsKBZoGTRahfmA5GA87JmqeHCEmfT478Pp","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f2447ad4-4108-4da4-b101-06f8e73459d8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f2447ad4-4108-4da4-b101-06f8e73459d8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3c3VPMMD2GNBSjN8o2oHdASEGKCDGZefdYoCMQmGP5v7HRyXxhrBxN6yyoJsn2RERiMQCpshd5p6gJ2VUVpMpWoN","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a90536cb-d27d-4901-a16a-8fc6099157e4\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a90536cb-d27d-4901-a16a-8fc6099157e4\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Xey44bx8ZxxMTh9U7qwUAG2jTSaHhkcD2EL93EnsS3XEhLahtDiCfyHYHxNtv4uRAaG6fXucbJTxAqZio1bvM4T","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4d958aab-e482-4bd9-9a40-bf0bd6176525\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4d958aab-e482-4bd9-9a40-bf0bd6176525\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4qunvLaW2jXqMyFLHDcfKHtiMfjHvG3HivSaqtoXbZGBV9DJMTmW5794nUC3ycpvY1HYG5TW6t9t4x5jZXMF8hBt","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"623374ee-d48f-45fb-9638-7e43f1910018\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"623374ee-d48f-45fb-9638-7e43f1910018\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:19 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Tgd4akFw5CPDUnWdQ5z8jfVXGtQm5BMCfRba6oiKBn15cdKaf5gYfGAwWEWAv4WqSopgGMqaYE9UZBW7i3HLmG2","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"aa32bdc4-9581-4f10-8595-5bca9d14c8a2\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"aa32bdc4-9581-4f10-8595-5bca9d14c8a2\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:21 INFO [bulk-wallet-service] Starting bulk wallet removal {"metadata":{"userId":"7059673708","walletCount":1}}
2025-05-29 14:25:21 INFO [wallet-tracker] Wallet bulk_removed {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userId":"7059673708","bulkOperation":true}}
2025-05-29 14:25:21 INFO [bulk-wallet-service] Bulk wallet removal completed {"metadata":{"userId":"7059673708","totalProcessed":1,"successful":1,"failed":0,"skipped":0,"results":[{"address":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","name":"","success":true,"action":"removed"}],"duration":38}}
2025-05-29 14:25:21 INFO [bulk-wallet-command] Bulk remove operation completed {"metadata":{"userId":"7059673708","totalProcessed":1,"successful":1,"failed":0,"skipped":0}}
2025-05-29 14:25:22 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4iibwkmGHPJapkMEFhTXcxkkMdXyycDt7puQQtuzBsexhTgSkHYAmSRpnUGrMMuJ2H7aed3o19KMGwKpcgFsYyn6","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ccfaae1c-f38f-4e25-b24f-59794c424e0d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ccfaae1c-f38f-4e25-b24f-59794c424e0d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:23 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3L5hJ5UrDew1LS6oQthPod5JRfKzzfzaipeBjYe4FYNDSNaqNkhnpQoYp2d6pMJhK5NrYbVjHWTn3XUMqc48uzHx","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f082da6b-4bee-4e94-86b1-24deae7c19a1\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f082da6b-4bee-4e94-86b1-24deae7c19a1\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:23 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:30 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:31 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:31 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"1tHx6ip3hV8QQhdGoMYcPMAxpug7fwPRZQ4w95BjMVGKd5E2PrGZzNsKBZoGTRahfmA5GA87JmqeHCEmfT478Pp","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ff39e954-d454-48f4-bca8-bf8b9a6522ef\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ff39e954-d454-48f4-bca8-bf8b9a6522ef\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:31 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3GE6jpw5vDECxpKpb8GtqFYDQBGHDgcE4F3zMPdCB11LLnvjL4w6wHWqnBhW8GHesYfQnN6EjC7C8tmwHwaY4wTB","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4ded8d08-b43c-426f-901f-4776f3cf809f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4ded8d08-b43c-426f-901f-4776f3cf809f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3c3VPMMD2GNBSjN8o2oHdASEGKCDGZefdYoCMQmGP5v7HRyXxhrBxN6yyoJsn2RERiMQCpshd5p6gJ2VUVpMpWoN","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"84491f61-0494-4b21-92f8-3e15656919b5\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"84491f61-0494-4b21-92f8-3e15656919b5\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Xey44bx8ZxxMTh9U7qwUAG2jTSaHhkcD2EL93EnsS3XEhLahtDiCfyHYHxNtv4uRAaG6fXucbJTxAqZio1bvM4T","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4205df70-3fc6-4420-9d2b-2c22c6ac75d1\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"4205df70-3fc6-4420-9d2b-2c22c6ac75d1\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4qunvLaW2jXqMyFLHDcfKHtiMfjHvG3HivSaqtoXbZGBV9DJMTmW5794nUC3ycpvY1HYG5TW6t9t4x5jZXMF8hBt","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"32c60ff7-1c06-45fb-a9e2-ff47c3918dbc\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"32c60ff7-1c06-45fb-a9e2-ff47c3918dbc\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:32 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2Tgd4akFw5CPDUnWdQ5z8jfVXGtQm5BMCfRba6oiKBn15cdKaf5gYfGAwWEWAv4WqSopgGMqaYE9UZBW7i3HLmG2","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d0876d94-e3ab-461a-9acf-ba45b4f9e794\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d0876d94-e3ab-461a-9acf-ba45b4f9e794\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:39 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4iibwkmGHPJapkMEFhTXcxkkMdXyycDt7puQQtuzBsexhTgSkHYAmSRpnUGrMMuJ2H7aed3o19KMGwKpcgFsYyn6","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"03424676-75ff-4a5c-85ed-b32f5a01c217\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"03424676-75ff-4a5c-85ed-b32f5a01c217\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:39 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3L5hJ5UrDew1LS6oQthPod5JRfKzzfzaipeBjYe4FYNDSNaqNkhnpQoYp2d6pMJhK5NrYbVjHWTn3XUMqc48uzHx","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ef5c236e-216b-48b2-a9b7-5ff6bde6497a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ef5c236e-216b-48b2-a9b7-5ff6bde6497a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:25:39 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:25:40 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:32:48 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":1,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com"]}}
2025-05-29 14:32:49 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 14:32:50 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 14:33:15 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userCount":1}}
2025-05-29 14:33:15 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:44 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:45 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"2wLSU5VtHTLJRiJNTS1xgDYrHJVGVgM2gYUNNEb9uJyz1nCRGc5K2tm9LSCqSnSFeWR6J6pTe4VhbgcLuB6hAqfe","metadata":{"attempt":1,"duration":305}}
2025-05-29 14:33:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:45 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"aEwqNLuuixS73DoidSTDsFSJv6Vt4oLyEevUttNftGdk75ZTJMciPoaS4YPc9ANA6Upqr2521e6NGwRwQ6W2d61","metadata":{"attempt":1,"duration":314}}
2025-05-29 14:33:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5W5CkgXYY4swbXapME4eRB4tmXBPmawew9ytVEmfEPQCMwwRgzJnzop1pNjtYtHLSJT4JsbE8U9U4bjusgMXftHd","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"01414283-515e-44cd-9786-06646c5bf038\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"01414283-515e-44cd-9786-06646c5bf038\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2GXbPZc1u8hUgn66ZHtKfVdGy8fSjghNvScdukRsACN26n66ySCb1tSrG6to2Hg9jZ3C87k6SqFpJQ6aaaGpmLsL","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c427a12e-4b79-41f7-b479-4616aa2edb8e\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c427a12e-4b79-41f7-b479-4616aa2edb8e\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3NgnbVyzYffWY1q7yameqqX2Po55JDRMN3FB1FHYtqELEuR7yFTJDu6h7zbM1mBhPs5gcExyUbm4QSDCiS2yK925","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ec57069e-e41a-40ba-8863-49d09ea22080\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ec57069e-e41a-40ba-8863-49d09ea22080\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4PFiHFPzHib4dRUjDvAmeqt4aRYhW7wf8KDu8MxnSyMehrLAfeW1gR7dRssXnhDhYZXkYRnAzaimC2XjVTNhmbhn","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7eb90fc1-28cb-4234-9d4e-87d16b041187\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7eb90fc1-28cb-4234-9d4e-87d16b041187\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4wy4Sjcjxsih6vTCDG42vCafSqjBzfnRA4XZap82kgJRrNDW6ZrFiG1zJDJYZwB8UKrZ8vETkaJLxkQnEsA9oHVa","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a05e3a44-a8cb-4bb4-b7e6-e899fae7bf1c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a05e3a44-a8cb-4bb4-b7e6-e899fae7bf1c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2NMLeaTaR2PifuEaXTHCkZHE7PDa8z4fWzbjVJkHGtXvCV6QrPjLdDUQhvjFb7xV8x9NpYP7evZFaiszq1HNK6F7","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9c235793-1ee7-4391-abf2-994e366642c7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9c235793-1ee7-4391-abf2-994e366642c7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"R6K659YTmV8G6n33zmmBmaJi8DqZWEuAthRPjpPzeSLMw7kP2SuAqaPPtThUe49U8TaMuLWJAGNRL2dBt9fBpSS","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"30690775-dc8a-449b-9022-7a50155dc2e9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"30690775-dc8a-449b-9022-7a50155dc2e9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y6uvaLyDS52GAoRH5C3MzuqwKpPaLW829khq56HEKu1csQmfNKKxhRGSW8BbVJb4Teyh5bDSASBZosc4drhBLYj","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a1fc0326-f37c-4f99-b317-793fc65a5afc\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a1fc0326-f37c-4f99-b317-793fc65a5afc\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5LPZXGcyYeFp55yV86aqbuSJn4pVmY6DMhxwVDhvrvdQzi5XEqVAeMmMGwgZREA2RyZ9t1k7yjCynDE2Hj88Zvhv","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41e6b003-e47c-4941-82b9-a5ed749e28c2\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"41e6b003-e47c-4941-82b9-a5ed749e28c2\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:33:54 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userCount":1}}
2025-05-29 14:33:54 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:33:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2GXbPZc1u8hUgn66ZHtKfVdGy8fSjghNvScdukRsACN26n66ySCb1tSrG6to2Hg9jZ3C87k6SqFpJQ6aaaGpmLsL","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a9c00116-9b43-4f0c-bd91-3c04eb5f554f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a9c00116-9b43-4f0c-bd91-3c04eb5f554f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5W5CkgXYY4swbXapME4eRB4tmXBPmawew9ytVEmfEPQCMwwRgzJnzop1pNjtYtHLSJT4JsbE8U9U4bjusgMXftHd","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d1828da8-a9bf-489a-a3c1-83f3859be7d3\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d1828da8-a9bf-489a-a3c1-83f3859be7d3\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3NgnbVyzYffWY1q7yameqqX2Po55JDRMN3FB1FHYtqELEuR7yFTJDu6h7zbM1mBhPs5gcExyUbm4QSDCiS2yK925","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7eb82cb6-623f-4cf5-bec4-50f05065f06d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7eb82cb6-623f-4cf5-bec4-50f05065f06d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5LPZXGcyYeFp55yV86aqbuSJn4pVmY6DMhxwVDhvrvdQzi5XEqVAeMmMGwgZREA2RyZ9t1k7yjCynDE2Hj88Zvhv","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"23b3743a-b657-49f3-8431-87989fcd7e84\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"23b3743a-b657-49f3-8431-87989fcd7e84\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4PFiHFPzHib4dRUjDvAmeqt4aRYhW7wf8KDu8MxnSyMehrLAfeW1gR7dRssXnhDhYZXkYRnAzaimC2XjVTNhmbhn","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f15e530a-93cc-4a77-b58a-817f2ef744c0\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f15e530a-93cc-4a77-b58a-817f2ef744c0\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4wy4Sjcjxsih6vTCDG42vCafSqjBzfnRA4XZap82kgJRrNDW6ZrFiG1zJDJYZwB8UKrZ8vETkaJLxkQnEsA9oHVa","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7aa12b08-7deb-4b61-983d-18c00cf87d72\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7aa12b08-7deb-4b61-983d-18c00cf87d72\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2NMLeaTaR2PifuEaXTHCkZHE7PDa8z4fWzbjVJkHGtXvCV6QrPjLdDUQhvjFb7xV8x9NpYP7evZFaiszq1HNK6F7","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c7dbec08-60ee-4fe1-a549-0397819ec5a0\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c7dbec08-60ee-4fe1-a549-0397819ec5a0\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y6uvaLyDS52GAoRH5C3MzuqwKpPaLW829khq56HEKu1csQmfNKKxhRGSW8BbVJb4Teyh5bDSASBZosc4drhBLYj","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"11c50637-71bb-4e1d-9db6-8cc9f9691157\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"11c50637-71bb-4e1d-9db6-8cc9f9691157\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"R6K659YTmV8G6n33zmmBmaJi8DqZWEuAthRPjpPzeSLMw7kP2SuAqaPPtThUe49U8TaMuLWJAGNRL2dBt9fBpSS","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c77e8202-a51c-493a-9633-fde3270ef576\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c77e8202-a51c-493a-9633-fde3270ef576\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:07 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5W5CkgXYY4swbXapME4eRB4tmXBPmawew9ytVEmfEPQCMwwRgzJnzop1pNjtYtHLSJT4JsbE8U9U4bjusgMXftHd","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f9ccdf81-51ef-47af-8cca-4b28f09c9aea\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f9ccdf81-51ef-47af-8cca-4b28f09c9aea\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2GXbPZc1u8hUgn66ZHtKfVdGy8fSjghNvScdukRsACN26n66ySCb1tSrG6to2Hg9jZ3C87k6SqFpJQ6aaaGpmLsL","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ea3a286a-0239-4ed0-8f2a-76a196b68f7c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ea3a286a-0239-4ed0-8f2a-76a196b68f7c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3NgnbVyzYffWY1q7yameqqX2Po55JDRMN3FB1FHYtqELEuR7yFTJDu6h7zbM1mBhPs5gcExyUbm4QSDCiS2yK925","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"24badceb-7208-450b-835e-e213e1b70270\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"24badceb-7208-450b-835e-e213e1b70270\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4wy4Sjcjxsih6vTCDG42vCafSqjBzfnRA4XZap82kgJRrNDW6ZrFiG1zJDJYZwB8UKrZ8vETkaJLxkQnEsA9oHVa","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f10384eb-0b97-43b2-9c77-261061ee9304\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f10384eb-0b97-43b2-9c77-261061ee9304\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y6uvaLyDS52GAoRH5C3MzuqwKpPaLW829khq56HEKu1csQmfNKKxhRGSW8BbVJb4Teyh5bDSASBZosc4drhBLYj","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"cb9c747e-5625-4ffc-988d-cb92383d3d69\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"cb9c747e-5625-4ffc-988d-cb92383d3d69\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2NMLeaTaR2PifuEaXTHCkZHE7PDa8z4fWzbjVJkHGtXvCV6QrPjLdDUQhvjFb7xV8x9NpYP7evZFaiszq1HNK6F7","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e89cb36a-128c-422d-b907-925d3d079916\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e89cb36a-128c-422d-b907-925d3d079916\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4PFiHFPzHib4dRUjDvAmeqt4aRYhW7wf8KDu8MxnSyMehrLAfeW1gR7dRssXnhDhYZXkYRnAzaimC2XjVTNhmbhn","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c0bb1ab0-7ec3-44a8-9107-f2b5ca8fcc3b\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c0bb1ab0-7ec3-44a8-9107-f2b5ca8fcc3b\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5LPZXGcyYeFp55yV86aqbuSJn4pVmY6DMhxwVDhvrvdQzi5XEqVAeMmMGwgZREA2RyZ9t1k7yjCynDE2Hj88Zvhv","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7b16b538-ca08-4b33-a107-76457cb9232c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7b16b538-ca08-4b33-a107-76457cb9232c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:16 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"R6K659YTmV8G6n33zmmBmaJi8DqZWEuAthRPjpPzeSLMw7kP2SuAqaPPtThUe49U8TaMuLWJAGNRL2dBt9fBpSS","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c8672e0e-5cb9-4331-bd1a-63e7f4e57444\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c8672e0e-5cb9-4331-bd1a-63e7f4e57444\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:17 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:17 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:17 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"33Q1nhvu7kNZqZJ3im8QeK7e87fAE9AFkTTnWTcgRKtJsfvSzZefsQgnVdF5XsZWTvqDYKAd6pzSqH3ZEdVtUJAn","metadata":{"attempt":1,"duration":308}}
2025-05-29 14:34:17 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:17 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"33Q1nhvu7kNZqZJ3im8QeK7e87fAE9AFkTTnWTcgRKtJsfvSzZefsQgnVdF5XsZWTvqDYKAd6pzSqH3ZEdVtUJAn","metadata":{"attempt":1,"duration":382}}
2025-05-29 14:34:17 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:19 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:19 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:27 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:27 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:27 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2tSxVi5TeRmLHShsfticL7msKsrt59tFQnoga8ybzD63KPpK7TLWjTzRQemDpHQ4jdnbqRTVKvhnyoWfewGQZhos","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f5696c6-74c7-4420-818c-bb39f701cb8f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1f5696c6-74c7-4420-818c-bb39f701cb8f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:27 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2tSxVi5TeRmLHShsfticL7msKsrt59tFQnoga8ybzD63KPpK7TLWjTzRQemDpHQ4jdnbqRTVKvhnyoWfewGQZhos","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"acd64a6e-b754-4769-a656-2cfddfeb13f0\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"acd64a6e-b754-4769-a656-2cfddfeb13f0\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:29 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":1}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2GXbPZc1u8hUgn66ZHtKfVdGy8fSjghNvScdukRsACN26n66ySCb1tSrG6to2Hg9jZ3C87k6SqFpJQ6aaaGpmLsL","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5f4216a1-e254-473e-b162-dabdba70d7b5\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"5f4216a1-e254-473e-b162-dabdba70d7b5\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5W5CkgXYY4swbXapME4eRB4tmXBPmawew9ytVEmfEPQCMwwRgzJnzop1pNjtYtHLSJT4JsbE8U9U4bjusgMXftHd","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1ce276cf-8c6f-4efc-972f-d1abac780905\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1ce276cf-8c6f-4efc-972f-d1abac780905\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3NgnbVyzYffWY1q7yameqqX2Po55JDRMN3FB1FHYtqELEuR7yFTJDu6h7zbM1mBhPs5gcExyUbm4QSDCiS2yK925","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7fe2f78d-989e-49b2-a1a8-b2dad3c77a61\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"7fe2f78d-989e-49b2-a1a8-b2dad3c77a61\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5LPZXGcyYeFp55yV86aqbuSJn4pVmY6DMhxwVDhvrvdQzi5XEqVAeMmMGwgZREA2RyZ9t1k7yjCynDE2Hj88Zvhv","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ce109dc2-857e-42b8-8a4e-9f773d4596ac\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"ce109dc2-857e-42b8-8a4e-9f773d4596ac\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2NMLeaTaR2PifuEaXTHCkZHE7PDa8z4fWzbjVJkHGtXvCV6QrPjLdDUQhvjFb7xV8x9NpYP7evZFaiszq1HNK6F7","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dd3346f1-298c-4351-ad07-c8bd8a9acf5f\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"dd3346f1-298c-4351-ad07-c8bd8a9acf5f\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"y6uvaLyDS52GAoRH5C3MzuqwKpPaLW829khq56HEKu1csQmfNKKxhRGSW8BbVJb4Teyh5bDSASBZosc4drhBLYj","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9773458d-1031-4758-93d4-716886be24f8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9773458d-1031-4758-93d4-716886be24f8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"R6K659YTmV8G6n33zmmBmaJi8DqZWEuAthRPjpPzeSLMw7kP2SuAqaPPtThUe49U8TaMuLWJAGNRL2dBt9fBpSS","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d976bdd3-d919-4f0c-89f2-b0f9e4962f6a\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d976bdd3-d919-4f0c-89f2-b0f9e4962f6a\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4PFiHFPzHib4dRUjDvAmeqt4aRYhW7wf8KDu8MxnSyMehrLAfeW1gR7dRssXnhDhYZXkYRnAzaimC2XjVTNhmbhn","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"02d026b4-dc58-4a23-9329-8c5747dd3324\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"02d026b4-dc58-4a23-9329-8c5747dd3324\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 14:34:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4wy4Sjcjxsih6vTCDG42vCafSqjBzfnRA4XZap82kgJRrNDW6ZrFiG1zJDJYZwB8UKrZ8vETkaJLxkQnEsA9oHVa","metadata":{"attempt":4,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2116b3b2-ae6d-4ac6-ad40-a949af1b771c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2116b3b2-ae6d-4ac6-ad40-a949af1b771c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:30:43 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":1,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com"]}}
2025-05-29 15:30:44 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 15:30:44 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 15:38:19 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":4,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://mainnet.helius-rpc.com/","https://mainnet.helius-rpc.com/","https://rpc.ankr.com/solana"]}}
2025-05-29 15:38:20 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 15:38:20 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 15:42:01 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":4,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://mainnet.helius-rpc.com/","https://mainnet.helius-rpc.com/","https://rpc.ankr.com/solana"]}}
2025-05-29 15:42:01 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 15:42:01 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 15:46:21 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":4,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://mainnet.helius-rpc.com/","https://mainnet.helius-rpc.com/","https://rpc.ankr.com/solana"]}}
2025-05-29 15:46:22 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 15:46:22 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 15:49:07 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":4,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://mainnet.helius-rpc.com/","https://mainnet.helius-rpc.com/","https://rpc.ankr.com/solana"]}}
2025-05-29 15:49:07 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 15:49:07 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 15:50:20 INFO [wallet-tracker] Wallet tracking_started {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"walletName":"","userCount":1}}
2025-05-29 15:50:20 INFO [wallet-tracker] Wallet subscription_created {"walletAddress":"o7RY6P2vQMuGSu1TrLM81weuzgDjaCRTXYRaXJwWcvc","metadata":{"subscriptionId":0,"attempt":1,"connectionType":"helius"}}
2025-05-29 15:50:45 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:50:46 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"23MS7ghTXmLm8H2NreLUik3gVD4ucMDMbv4qZ3oXHpj9ZmtPpCgLUe45mjus51kYTjLx8Zrm8f3TCV11muqhK5hY","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:48 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54ZF2zpGBoJguvLB5J6gVCDfduneWEL8NBGEQZ5HBHG3cPbfZw7GXr4nK2cyZwHqJQn8VesLXYSAd5BHFCeq15jP","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:48 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:48 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"23MS7ghTXmLm8H2NreLUik3gVD4ucMDMbv4qZ3oXHpj9ZmtPpCgLUe45mjus51kYTjLx8Zrm8f3TCV11muqhK5hY","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:50 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:50:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54ZF2zpGBoJguvLB5J6gVCDfduneWEL8NBGEQZ5HBHG3cPbfZw7GXr4nK2cyZwHqJQn8VesLXYSAd5BHFCeq15jP","metadata":{"attempt":2,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:52 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:53 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"23MS7ghTXmLm8H2NreLUik3gVD4ucMDMbv4qZ3oXHpj9ZmtPpCgLUe45mjus51kYTjLx8Zrm8f3TCV11muqhK5hY","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:54 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54ZF2zpGBoJguvLB5J6gVCDfduneWEL8NBGEQZ5HBHG3cPbfZw7GXr4nK2cyZwHqJQn8VesLXYSAd5BHFCeq15jP","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"29JKJokGx733jbAuqAHhU9QwkoLwroF9bwXFx1GNra6QRipuafgyDnwNWdM9z4eCAm8redNVe39jn5wVxntKW3Lr","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qRE8nRGpaZyn8YLK6j8FBFqzyTLpVWJ1CFwYjHyx9knARmBazsF7xLwKdFn7LZNAvL9ztg6eXEoWu7KYigNf4Ke","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4eJvwrjzMhapVdFRxJtadmuqXtzce7fMJbJtCKifa2vwv4nzduGsiXoAwDYLyvz16QTMXSMrSzMPUh291eAUzzCD","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"rXPpDz8rT36DGiZEfi9RRiJAayRVLyGkXMwMjS1E5fuBcvgUH7xzHgyWUo3N3CJ9toV8gSB4Pyrqj46fHzEow8z","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kn2RVUJ8hxwGaS4xViXayjpS9qFEVK6NYnohohGmYzJW9CcrXYMqhhGDw9fu8oJ1dY2mCLbBmgmy63kWafYyuej","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:50:55 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2ECHzhxQsx1ko4HYKA4XJKgx4GwxTHj3e1mSvzo7Y19PYWSkYi1vzMptqttC1KmFC3rSJ3XWRPUXDfqhGNkyRNdF","metadata":{"attempt":1,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5Wo3f7nTizCxjuwPi1ybKowwU3L9jyd3qPLh7PM1eHxjzQ3LD7aKY8Wee5pGQxYwb4yNEVDgAFSkd4ZU1F7v4sJe","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:56 DEBUG [api-client] API call to RPC succeeded {"duration":984,"metadata":{"provider":"RPC","endpoint":"https://api.mainnet-beta.solana.com/getParsedTransactions","success":true}}
2025-05-29 15:50:56 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"5wRDtt9xc2gtoTLeN9VT7dcxvmnZGBNHwiy2dQhsNkSki6n542owoUK3CwLDdtbPHGvwEw1FfePNihpYj12SSAMj","metadata":{"attempt":1,"duration":984}}
2025-05-29 15:50:56 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"28kSCJX3KG3p9jhsXXdcn1MYregrUMivxxwe8bmERaaWubEm3fvX7Rxz1CpZTsH4tT8Gx4YdpttLhAMFgiyfbxNY","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4H4Ukgw8QZaYTxRc6VjHMhCvJXog3iTDELa4irrPMTbBEvRWkbU4smXnDBesNgoY11gKsyM8efdmsReFj7X62vkP","metadata":{"attempt":1,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"29JKJokGx733jbAuqAHhU9QwkoLwroF9bwXFx1GNra6QRipuafgyDnwNWdM9z4eCAm8redNVe39jn5wVxntKW3Lr","metadata":{"attempt":2,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"rXPpDz8rT36DGiZEfi9RRiJAayRVLyGkXMwMjS1E5fuBcvgUH7xzHgyWUo3N3CJ9toV8gSB4Pyrqj46fHzEow8z","metadata":{"attempt":2,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:57 DEBUG [api-client] API call to RPC succeeded {"duration":2824,"metadata":{"provider":"RPC","endpoint":"https://api.mainnet-beta.solana.com/getParsedTransactions","success":true}}
2025-05-29 15:50:57 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"3TXszg2RSSKtb149CDSK4cJkZ5XtnK6zSwyiXo9AM32STEzCe8fYHr79ZiXGMT5vbe8VtBVdzuvBTJzs27rUkaFn","metadata":{"attempt":1,"duration":2824}}
2025-05-29 15:50:57 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:50:58 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:58 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:50:58 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:50:58 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4H4Ukgw8QZaYTxRc6VjHMhCvJXog3iTDELa4irrPMTbBEvRWkbU4smXnDBesNgoY11gKsyM8efdmsReFj7X62vkP","metadata":{"attempt":2,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"23MS7ghTXmLm8H2NreLUik3gVD4ucMDMbv4qZ3oXHpj9ZmtPpCgLUe45mjus51kYTjLx8Zrm8f3TCV11muqhK5hY","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:01 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"rXPpDz8rT36DGiZEfi9RRiJAayRVLyGkXMwMjS1E5fuBcvgUH7xzHgyWUo3N3CJ9toV8gSB4Pyrqj46fHzEow8z","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:01 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"29JKJokGx733jbAuqAHhU9QwkoLwroF9bwXFx1GNra6QRipuafgyDnwNWdM9z4eCAm8redNVe39jn5wVxntKW3Lr","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:02 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:51:03 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4H4Ukgw8QZaYTxRc6VjHMhCvJXog3iTDELa4irrPMTbBEvRWkbU4smXnDBesNgoY11gKsyM8efdmsReFj7X62vkP","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"54ZF2zpGBoJguvLB5J6gVCDfduneWEL8NBGEQZ5HBHG3cPbfZw7GXr4nK2cyZwHqJQn8VesLXYSAd5BHFCeq15jP","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:06 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2bMPGsex66pZzRtLDtiErgMwKv8bn55ChtzkRwtcVL5qzoYXBbNTZ8ZfeQk2oVGtANdXcGwHz44HByx9aEGfa175","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"795903bc-c316-409a-947f-e22686879228\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"795903bc-c316-409a-947f-e22686879228\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:08 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:51:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qRE8nRGpaZyn8YLK6j8FBFqzyTLpVWJ1CFwYjHyx9knARmBazsF7xLwKdFn7LZNAvL9ztg6eXEoWu7KYigNf4Ke","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c9411932-93a2-4dc4-9368-2682e3b888f8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c9411932-93a2-4dc4-9368-2682e3b888f8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4eJvwrjzMhapVdFRxJtadmuqXtzce7fMJbJtCKifa2vwv4nzduGsiXoAwDYLyvz16QTMXSMrSzMPUh291eAUzzCD","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2eb14919-9598-4482-a4ef-8e301fc81ac5\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"2eb14919-9598-4482-a4ef-8e301fc81ac5\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5Wo3f7nTizCxjuwPi1ybKowwU3L9jyd3qPLh7PM1eHxjzQ3LD7aKY8Wee5pGQxYwb4yNEVDgAFSkd4ZU1F7v4sJe","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"abe10a4c-2918-445d-a470-1eb58cc0bf7c\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"abe10a4c-2918-445d-a470-1eb58cc0bf7c\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kn2RVUJ8hxwGaS4xViXayjpS9qFEVK6NYnohohGmYzJW9CcrXYMqhhGDw9fu8oJ1dY2mCLbBmgmy63kWafYyuej","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"644c1bda-396d-4b98-a957-01d45cce9964\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"644c1bda-396d-4b98-a957-01d45cce9964\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:09 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2ECHzhxQsx1ko4HYKA4XJKgx4GwxTHj3e1mSvzo7Y19PYWSkYi1vzMptqttC1KmFC3rSJ3XWRPUXDfqhGNkyRNdF","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"cdd55be4-f470-4b15-8ebe-0a6982babadb\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"cdd55be4-f470-4b15-8ebe-0a6982babadb\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:09 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:51:10 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"28kSCJX3KG3p9jhsXXdcn1MYregrUMivxxwe8bmERaaWubEm3fvX7Rxz1CpZTsH4tT8Gx4YdpttLhAMFgiyfbxNY","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"63814bdd-3198-498d-b026-24ef5c681c79\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"63814bdd-3198-498d-b026-24ef5c681c79\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"rXPpDz8rT36DGiZEfi9RRiJAayRVLyGkXMwMjS1E5fuBcvgUH7xzHgyWUo3N3CJ9toV8gSB4Pyrqj46fHzEow8z","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:10 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"29JKJokGx733jbAuqAHhU9QwkoLwroF9bwXFx1GNra6QRipuafgyDnwNWdM9z4eCAm8redNVe39jn5wVxntKW3Lr","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:11 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:11 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4H4Ukgw8QZaYTxRc6VjHMhCvJXog3iTDELa4irrPMTbBEvRWkbU4smXnDBesNgoY11gKsyM8efdmsReFj7X62vkP","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qRE8nRGpaZyn8YLK6j8FBFqzyTLpVWJ1CFwYjHyx9knARmBazsF7xLwKdFn7LZNAvL9ztg6eXEoWu7KYigNf4Ke","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4eJvwrjzMhapVdFRxJtadmuqXtzce7fMJbJtCKifa2vwv4nzduGsiXoAwDYLyvz16QTMXSMrSzMPUh291eAUzzCD","metadata":{"attempt":3,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:51:13 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5Wo3f7nTizCxjuwPi1ybKowwU3L9jyd3qPLh7PM1eHxjzQ3LD7aKY8Wee5pGQxYwb4yNEVDgAFSkd4ZU1F7v4sJe","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:51:13 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:51:14 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:51:20 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2bMPGsex66pZzRtLDtiErgMwKv8bn55ChtzkRwtcVL5qzoYXBbNTZ8ZfeQk2oVGtANdXcGwHz44HByx9aEGfa175","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"53c5002c-f99a-4d5b-a1fc-d6c38778c483\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"53c5002c-f99a-4d5b-a1fc-d6c38778c483\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:21 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:21 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qRE8nRGpaZyn8YLK6j8FBFqzyTLpVWJ1CFwYjHyx9knARmBazsF7xLwKdFn7LZNAvL9ztg6eXEoWu7KYigNf4Ke","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:21 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":0,"totalConnections":4}}
2025-05-29 15:51:21 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:21 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5Wo3f7nTizCxjuwPi1ybKowwU3L9jyd3qPLh7PM1eHxjzQ3LD7aKY8Wee5pGQxYwb4yNEVDgAFSkd4ZU1F7v4sJe","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:24 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:24 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2bMPGsex66pZzRtLDtiErgMwKv8bn55ChtzkRwtcVL5qzoYXBbNTZ8ZfeQk2oVGtANdXcGwHz44HByx9aEGfa175","metadata":{"attempt":3,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kn2RVUJ8hxwGaS4xViXayjpS9qFEVK6NYnohohGmYzJW9CcrXYMqhhGDw9fu8oJ1dY2mCLbBmgmy63kWafYyuej","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f46db94b-1bf2-4507-b5e9-68a88da010d7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"f46db94b-1bf2-4507-b5e9-68a88da010d7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2ECHzhxQsx1ko4HYKA4XJKgx4GwxTHj3e1mSvzo7Y19PYWSkYi1vzMptqttC1KmFC3rSJ3XWRPUXDfqhGNkyRNdF","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a0a26e0f-353c-4e58-a98d-5d00c7aacdf9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"a0a26e0f-353c-4e58-a98d-5d00c7aacdf9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:25 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"28kSCJX3KG3p9jhsXXdcn1MYregrUMivxxwe8bmERaaWubEm3fvX7Rxz1CpZTsH4tT8Gx4YdpttLhAMFgiyfbxNY","metadata":{"attempt":3,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c328bf2-dad4-460d-95e5-c0e9e0864293\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c328bf2-dad4-460d-95e5-c0e9e0864293\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:28 DEBUG [api-client] API call to RPC succeeded {"duration":7095,"metadata":{"provider":"RPC","endpoint":"https://api.mainnet-beta.solana.com/getParsedTransactions","success":true}}
2025-05-29 15:51:28 INFO [transaction-parser] Transaction fetched_successfully {"transactionSignature":"4eJvwrjzMhapVdFRxJtadmuqXtzce7fMJbJtCKifa2vwv4nzduGsiXoAwDYLyvz16QTMXSMrSzMPUh291eAUzzCD","metadata":{"attempt":4,"duration":7095}}
2025-05-29 15:51:28 WARN [transaction-fetcher] getParsedTransaction slow (33210ms) {"duration":33210,"metadata":{"isSlowOperation":true}}
2025-05-29 15:51:28 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":2,"totalConnections":4}}
2025-05-29 15:51:32 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2bMPGsex66pZzRtLDtiErgMwKv8bn55ChtzkRwtcVL5qzoYXBbNTZ8ZfeQk2oVGtANdXcGwHz44HByx9aEGfa175","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:33 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":3,"totalConnections":4}}
2025-05-29 15:51:33 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2kn2RVUJ8hxwGaS4xViXayjpS9qFEVK6NYnohohGmYzJW9CcrXYMqhhGDw9fu8oJ1dY2mCLbBmgmy63kWafYyuej","metadata":{"attempt":4,"retries":4},"error":{"message":"403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","stack":"Error: 403 Forbidden: {\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:33 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2ECHzhxQsx1ko4HYKA4XJKgx4GwxTHj3e1mSvzo7Y19PYWSkYi1vzMptqttC1KmFC3rSJ3XWRPUXDfqhGNkyRNdF","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 15:51:33 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":4}}
2025-05-29 15:51:34 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"28kSCJX3KG3p9jhsXXdcn1MYregrUMivxxwe8bmERaaWubEm3fvX7Rxz1CpZTsH4tT8Gx4YdpttLhAMFgiyfbxNY","metadata":{"attempt":4,"retries":4},"error":{"message":"401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}","stack":"Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"Batch requests are only available for paid plans. Please upgrade if you would like to gain access\"}}\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\@solana+web3.js@1.98.0_bufferutil@4.0.9_utf-8-validate@5.0.10\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 16:00:00 INFO [statistics] Hourly statistics {"metadata":{"timestamp":"2025-05-29T15:00:00.496Z","rpcConnections":{"total":4,"healthy":4,"failed":0},"heliusConnections":{"total":2,"available":2,"failed":0},"wallets":{"total":0,"active":0,"banned":1},"memoryUsagePercent":89,"uptimeHours":0,"errorsLastHour":0,"criticalErrors":0}}
2025-05-29 16:02:13 INFO  Loaded 2 Helius API keys 
2025-05-29 16:02:13 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":5,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com","https://api.metaplex.solana.com","https://solana-mainnet.rpc.extrnode.com"]}}
2025-05-29 16:02:13 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 16:02:14 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
