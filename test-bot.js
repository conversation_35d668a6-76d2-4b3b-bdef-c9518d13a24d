// Simple test to check if the bot can start
require('dotenv').config()
const TelegramBot = require('node-telegram-bot-api')

const BOT_TOKEN = process.env.BOT_TOKEN

console.log('🤖 Testing Telegram bot connection...')
console.log('Bot Token:', BOT_TOKEN ? 'Found' : 'Missing')

if (!BOT_TOKEN) {
  console.error('❌ BOT_TOKEN not found in .env file')
  process.exit(1)
}

// Create bot with polling
const bot = new TelegramBot(BOT_TOKEN, { 
  polling: {
    interval: 1000,
    autoStart: true,
    params: {
      timeout: 10,
    }
  }
})

console.log('✅ Bot created with polling mode')

// Test basic commands
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id
  console.log(`📱 Received /start from chat ID: ${chatId}`)
  bot.sendMessage(chatId, '🎉 <PERSON><PERSON> is working! Enhanced Handi Cat Wallet Tracker is online.')
})

bot.onText(/\/test/, (msg) => {
  const chatId = msg.chat.id
  console.log(`📱 Received /test from chat ID: ${chatId}`)
  bot.sendMessage(chatId, '✅ Test successful! The bot is responding to commands.')
})

bot.onText(/\/help/, (msg) => {
  const chatId = msg.chat.id
  console.log(`📱 Received /help from chat ID: ${chatId}`)
  bot.sendMessage(chatId, `
🤖 <b>Enhanced Handi Cat Wallet Tracker</b>

✅ <b>Bot Status:</b> Online and working!

🔥 <b>Available Commands:</b>
/start - Start the bot
/test - Test bot functionality  
/help - Show this help

🚀 <b>Enhanced Features:</b>
• Unlimited wallet tracking
• Bulk operations
• Real-time notifications
• Advanced monitoring

The bot is ready for unlimited Solana wallet tracking!
  `, { parse_mode: 'HTML' })
})

// Error handling
bot.on('error', (error) => {
  console.error('❌ Bot error:', error)
})

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error)
})

// Success message
bot.on('message', (msg) => {
  if (!msg.text || msg.text.startsWith('/')) return
  console.log(`📱 Received message from ${msg.from.first_name}: ${msg.text}`)
})

console.log('🚀 Bot is starting...')
console.log('📱 Send /start to your bot to test!')
console.log('🔍 Watching for messages...')

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down bot...')
  bot.stopPolling()
  process.exit(0)
})

// Test connection after 2 seconds
setTimeout(() => {
  console.log('✅ Bot has been running for 2 seconds - polling is working!')
  console.log('📱 Try sending /start to your bot now!')
}, 2000)
