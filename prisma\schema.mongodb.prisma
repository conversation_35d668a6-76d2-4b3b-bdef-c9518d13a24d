// MongoDB-compatible Prisma schema
// Use this file if you want to use MongoDB instead of PostgreSQL/MySQL
// Copy this content to schema.prisma and set DATABASE_PROVIDER=mongodb

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum SubscriptionPlan {
  FREE
  HOBBY
  PRO
  WHALE
}

enum WalletStatus {
  ACTIVE
  USER_PAUSED
  SPAM_PAUSED
  BANNED
}

enum HandiCatStatus {
  ACTIVE
  PAUSED
}

enum PromotionType {
  UPGRADE_TO_50_WALLETS
}

model User {
  id                      String             @id @default(auto()) @map("_id") @db.ObjectId
  telegramId              String             @unique
  username                String
  firstName               String
  lastName                String

  hasDonated              Boolean          @default(false)

  botStatus               HandiCatStatus   @default(ACTIVE)

  personalWalletPubKey    String
  personalWalletPrivKey   String
  
  userSubscription        UserSubscription?
  userWallets             UserWallet[]
  userPromotions          UserPromotion[]
  groups                  Group[]

  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt

  @@map("users")
}

model Wallet {
  id            String         @id @default(auto()) @map("_id") @db.ObjectId
  address       String         @unique
  
  userWallets   UserWallet[]

  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@map("wallets")
}

model UserWallet {
  id              String           @id @default(auto()) @map("_id") @db.ObjectId
  userId          String           @db.ObjectId
  walletId        String           @db.ObjectId
  name            String
  address         String

  handiCatStatus  HandiCatStatus   @default(ACTIVE)
  status          WalletStatus     @default(ACTIVE)

  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  user            User             @relation(fields: [userId], references: [id])
  wallet          Wallet           @relation(fields: [walletId], references: [id])

  @@unique([userId, walletId])
  @@map("user_wallets")
}

model UserSubscription {
  id                             String            @id @default(auto()) @map("_id") @db.ObjectId
  plan                           SubscriptionPlan  @default(FREE)

  isCanceled                     Boolean           @default(false)
  subscriptionCurrentPeriodEnd   DateTime?

  createdAt                      DateTime          @default(now())
  updatedAt                      DateTime          @updatedAt

  userId                         String            @unique @db.ObjectId
  user                           User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_subscriptions")
}

model Promotion {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  name String
  type PromotionType
  price Float
  isActive Boolean @default(true)
  isStackable Boolean 

  userPromotions UserPromotion[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("promotions")
}

model UserPromotion {
   id String @id @default(auto()) @map("_id") @db.ObjectId

   purchasedAt DateTime  @default(now())

   userId String @db.ObjectId
   user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  promotionId String @db.ObjectId
  promotion    Promotion    @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@map("user_promotions")
}

model Group {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  name String

  userId String @db.ObjectId
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("groups")
}
