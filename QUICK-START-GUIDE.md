# 🚀 Quick Start Guide - Handi Cat Enhanced Edition

> **Get unlimited wallet tracking with bulk operations running in 5 minutes**

## ⚡ **Super Quick Setup**

### **1. Clone & Install**
```bash
git clone https://github.com/DracoR22/handi-cat_wallet-tracker.git
cd handi-cat_wallet-tracker
pnpm install
```

### **2. Configure Environment**
```bash
cp .env.example .env
```

**Edit `.env` with these essentials:**
```env
# Multiple Helius API Keys (NO SPACES between commas)
HELIUS_API_KEYS=key1,key2,key3,key4,key5

# Telegram Bot
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_CHAT_ID=your_telegram_chat_id

# Database (choose one)
DATABASE_URL=postgresql://user:pass@localhost:5432/wallet_tracker

# Enhanced Features
DISABLE_PAYMENTS=true
ENABLE_FILE_LOGGING=true
LOG_LEVEL=info
```

### **3. Setup Database**
```bash
pnpm db:setup
```

### **4. Start Bot**
```bash
pnpm start
```

**You'll see:**
```
🤖 Telegram bot initialized with POLLING mode
✅ Perfect for unlimited wallet tracking!
```

## 🎯 **Essential Commands**

### **📦 Bulk Operations (NEW)**

**Add Multiple Wallets:**
```
/bulk_add
```
Then send up to 100 wallets in any format:
```
wallet1 Whale Wallet
wallet2 DeFi Trader
wallet3,NFT Collector
```

**Remove Multiple Wallets:**
```
/bulk_remove
```
Then send addresses to remove:
```
wallet1
wallet2
wallet3
```

**View Your Wallets:**
```
/list_wallets
```

### **📊 Monitoring Commands**

**System Health:**
```
/health
```

**Error Summary:**
```
/errors
```

**View Logs:**
```
/logs
```

### **📝 Basic Commands**

**Single Wallet:**
```
/add    - Add one wallet
/delete - Remove one wallet
/manage - Manage wallets
```

**Help:**
```
/help   - All commands
/start  - Main menu
```

## 🔥 **Key Features You Get**

✅ **Unlimited wallet tracking** (no subscription limits)
✅ **Bulk add/remove** up to 100 wallets at once
✅ **Multiple Helius API keys** with load balancing
✅ **Real-time notifications** via Telegram
✅ **Automatic cleanup** after 5 days inactivity
✅ **Comprehensive monitoring** and error tracking
✅ **Polling mode** (no webhook setup needed)

## 📱 **What You'll Receive**

**Transaction Notifications:**
```
🟢 BUY DETECTED
💰 Wallet: Whale Wallet
🪙 Token: BONK
💵 Amount: 1,000,000 BONK
💲 Value: $156.78
🔗 Transaction: [View on Solscan]
```

**Bulk Operation Results:**
```
📊 Bulk Added Summary
✅ Successfully added: 45
❌ Failed: 2
⏭️ Skipped: 3
⏱️ Duration: 12.3s
```

**System Health Updates:**
```
🏥 System Health Summary
🔗 RPC: 5/5 healthy
🔑 Helius: 4/5 available
👛 Wallets: 150 active
```

## 🆓 **Free Resources**

**Databases:**
- [Supabase](https://supabase.com) - 500MB PostgreSQL free
- [PlanetScale](https://planetscale.com) - 5GB MySQL free
- [MongoDB Atlas](https://www.mongodb.com/atlas) - 512MB free

**RPC Endpoints:**
- [QuickNode](https://quicknode.com) - 100k requests/day
- [Alchemy](https://alchemy.com) - 300M compute units/month
- [Chainstack](https://chainstack.com) - 100k requests/day

## 🛠️ **Development Commands**

```bash
# Different log levels
pnpm start:debug    # Debug logging
pnpm start:verbose  # Maximum logging

# Database management
pnpm db:migrate     # Run migrations
pnpm db:studio      # Open Prisma Studio

# Log management
pnpm logs:view      # View current logs
pnpm logs:errors    # View error logs
pnpm logs:clean     # Clean log files
```

## 🚨 **Troubleshooting**

**"No Helius API keys configured"**
- Check `HELIUS_API_KEYS` in `.env`
- Ensure no spaces between keys: `key1,key2,key3`

**"Database connection failed"**
- Verify `DATABASE_URL` format
- Run `pnpm db:setup`

**"Bot not responding"**
- Check `BOT_TOKEN` is correct
- Verify bot is started with `/start`

**"Rate limit exceeded"**
- Add more Helius API keys
- Check API key usage limits

## 📊 **Scaling Guidelines**

| Wallets | API Keys | Setup |
|---------|----------|-------|
| 100-300 | 3 keys | Starter |
| 500-1000 | 5 keys | Professional |
| 1000+ | 10+ keys | Enterprise |

## 🎉 **You're Ready!**

**Your enhanced wallet tracker now supports:**
- ✅ Unlimited wallet tracking
- ✅ Bulk operations (up to 100 wallets)
- ✅ Multiple API key load balancing
- ✅ Comprehensive monitoring
- ✅ Automatic cleanup
- ✅ Real-time Telegram notifications

**Start tracking unlimited Solana wallets with bulk operations!** 🚀

---

**Need help?** Check the full [README.md](README.md) or use `/help` in the bot.
