import TelegramBot from 'node-telegram-bot-api'
import dotenv from 'dotenv'

dotenv.config()

const BOT_TOKEN = process.env.BOT_TOKEN
const TEST_BOT_TOKEN = process.env.TEST_BOT_TOKEN
const APP_URL = process.env.APP_URL

const WEBHOOK_URL = `https://api.telegram.org/bot${BOT_TOKEN}/setWebhook?url=${APP_URL}`

// ============================================================================
// TELEGRAM CONNECTION MODE CONFIGURATION
// ============================================================================

// POLLING MODE (Recommended for unlimited wallet tracking)
// ✅ Simple setup, no server configuration needed
// ✅ Works locally and in production
// ✅ Perfect for wallet tracking bots
export const bot = new TelegramBot(BOT_TOKEN ?? '', {
  polling: {
    interval: 1000,  // Check for updates every 1 second
    autoStart: true, // Start polling automatically
    params: {
      timeout: 10,   // Long polling timeout
    }
  }
})

console.log('🤖 Telegram bot initialized with POLLING mode')
console.log('✅ Perfect for unlimited wallet tracking!')

// WEBHOOK MODE (Only for production with public domain)
// Uncomment the section below and comment the polling section above
// if you want to use webhooks in production
/*
export const bot = new TelegramBot(BOT_TOKEN ?? '')

bot
  .setWebHook(WEBHOOK_URL)
  .then(() => {
    console.log(`Webhook set to ${WEBHOOK_URL}`)
  })
  .catch((error) => {
    console.error('Error setting webhook:', error)
  })
*/
