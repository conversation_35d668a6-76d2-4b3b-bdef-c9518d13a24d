# 📦 Bulk Wallet Management Guide

This guide explains how to use the new bulk wallet management features for adding and removing multiple wallets at once.

## 🚀 **New Commands Available**

### **Bulk Add Wallets**
```
/bulk_add
```
Add multiple wallets at once with progress tracking and detailed results.

### **Bulk Remove Wallets**
```
/bulk_remove
```
Remove multiple wallets at once with confirmation and progress tracking.

### **List Wallets**
```
/list_wallets
```
View your currently tracked wallets and get wallet count.

## 📝 **How to Use Bulk Add**

### **Step 1: Start Bulk Add**
Send `/bulk_add` to the bot.

### **Step 2: Send Wallet List**
The bot will ask you to send a list of wallets. You can use multiple formats:

**Format 1: Address Only**
```
5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S
```

**Format 2: Address with Name (Space Separated)**
```
5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S NFT Collector
```

**Format 3: Address with Name (Comma Separated)**
```
5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1,Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi,DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector
```

### **Step 3: Watch Progress**
The bot will show real-time progress:
```
🔄 Processing 50 wallets...

📊 Progress: 60%
🔍 Current: 5Q544fKr...e4j1
```

### **Step 4: View Results**
Get a detailed summary:
```
📊 Bulk Added Summary

✅ Successfully added: 45
❌ Failed: 2
⏭️ Skipped: 3
📝 Total processed: 50
⏱️ Duration: 12.3s

Results:
✅ 5Q544fKr...e4j1 Whale Wallet
✅ 7UX2i7Su...oDUi DeFi Trader
⏭️ 2ojv9BAi...HG8S Already tracking
❌ invalid123...addr Invalid address format
```

## 🗑️ **How to Use Bulk Remove**

### **Step 1: Start Bulk Remove**
Send `/bulk_remove` to the bot.

### **Step 2: Send Address List**
Send wallet addresses you want to remove (one per line):
```
5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S
```

### **Step 3: Watch Progress**
Real-time progress tracking:
```
🔄 Removing 25 wallets...

📊 Progress: 80%
🔍 Current: 7UX2i7Su...oDUi
```

### **Step 4: View Results**
Detailed removal summary:
```
📊 Bulk Removed Summary

✅ Successfully removed: 23
❌ Failed: 0
⏭️ Skipped: 2
📝 Total processed: 25
⏱️ Duration: 8.1s

Results:
✅ 5Q544fKr...e4j1 Whale Wallet
✅ 7UX2i7Su...oDUi DeFi Trader
⏭️ 2ojv9BAi...HG8S Not in tracking list
```

## 📊 **Features & Benefits**

### **Smart Validation**
- ✅ Validates Solana address format
- ✅ Checks for duplicate wallets
- ✅ Skips invalid addresses automatically
- ✅ Prevents adding already tracked wallets

### **Progress Tracking**
- ✅ Real-time progress updates
- ✅ Shows current wallet being processed
- ✅ Percentage completion
- ✅ Estimated time remaining

### **Error Handling**
- ✅ Continues processing even if some wallets fail
- ✅ Detailed error messages for each failure
- ✅ Comprehensive summary with all results
- ✅ Automatic retry for temporary failures

### **Performance Optimized**
- ✅ Processes up to 100 wallets per batch
- ✅ Small delays to prevent system overload
- ✅ Efficient database operations
- ✅ Automatic wallet tracking setup

## 🎯 **Use Cases**

### **For Whale Watchers**
```
# Add multiple whale wallets at once
/bulk_add

# Then send:
whale1_address Binance Hot Wallet
whale2_address Coinbase Institutional
whale3_address Jump Trading
whale4_address Alameda Research
whale5_address FTX Hot Wallet
```

### **For DeFi Traders**
```
# Track multiple DeFi protocols
/bulk_add

# Then send:
protocol1_treasury Uniswap Treasury
protocol2_treasury Compound Treasury  
protocol3_treasury Aave Treasury
protocol4_treasury Curve Treasury
```

### **For NFT Collectors**
```
# Track top NFT collectors
/bulk_add

# Then send:
collector1_address Top Collector 1
collector2_address Top Collector 2
collector3_address Top Collector 3
```

### **For Portfolio Management**
```
# Remove old wallets that are no longer active
/bulk_remove

# Then send list of inactive wallet addresses:
old_wallet_1
old_wallet_2
old_wallet_3
```

## ⚠️ **Limits & Guidelines**

### **Batch Limits**
- **Maximum per batch**: 100 wallets
- **Timeout**: 5 minutes to send wallet list
- **One operation at a time**: Can't run multiple bulk operations simultaneously

### **Best Practices**
1. **Prepare your list**: Have wallet addresses ready before starting
2. **Use descriptive names**: Help identify wallets later
3. **Check format**: Ensure addresses are valid Solana addresses
4. **Start small**: Test with a few wallets first
5. **Monitor results**: Check the summary for any issues

### **Error Prevention**
- ✅ Double-check wallet addresses before sending
- ✅ Use consistent naming conventions
- ✅ Remove any extra spaces or characters
- ✅ Don't include empty lines

## 🔧 **Troubleshooting**

### **Common Issues**

**"Invalid Solana address format"**
- Check that addresses are valid Solana public keys
- Remove any extra characters or spaces
- Ensure addresses are 44 characters long

**"Wallet already being tracked"**
- The wallet is already in your tracking list
- Use `/list_wallets` to see current wallets
- Remove duplicates from your input

**"Operation timed out"**
- You took too long to send the wallet list
- Start over with `/bulk_add` or `/bulk_remove`
- Prepare your list before starting

**"Too many wallets"**
- Limit to 100 wallets per batch
- Split large lists into multiple batches
- Process in smaller groups for better reliability

### **Getting Help**

If you encounter issues:
1. Check this guide for common solutions
2. Use `/help` for general bot commands
3. Try with a smaller batch first
4. Contact support if problems persist

## 📈 **Performance Tips**

### **For Large Lists (50+ wallets)**
- Split into batches of 25-50 wallets
- Wait for each batch to complete
- Monitor system performance

### **For Better Organization**
- Use consistent naming patterns
- Group related wallets together
- Keep a backup list of your wallet addresses

### **For Optimal Results**
- Use during off-peak hours
- Ensure stable internet connection
- Have wallet addresses pre-validated

## 🎉 **Summary**

The bulk wallet management features allow you to:

✅ **Add up to 100 wallets at once** with progress tracking
✅ **Remove multiple wallets quickly** with confirmation
✅ **Use flexible input formats** for addresses and names
✅ **Get detailed results** with success/failure breakdown
✅ **Handle errors gracefully** with automatic skipping
✅ **Track progress in real-time** with live updates

**Perfect for unlimited wallet tracking with multiple Helius API keys!** 🚀

---

**Commands Summary:**
- `/bulk_add` - Add multiple wallets
- `/bulk_remove` - Remove multiple wallets  
- `/list_wallets` - View tracked wallets
- `/help` - General help and commands
