@echo off
setlocal enabledelayedexpansion

REM Handi Cat Wallet Tracker Enhanced Edition - Windows Setup Script
REM =================================================================

echo.
echo 🐱 Handi Cat Wallet Tracker Enhanced Edition Setup
echo ==================================================
echo.

REM Check if Node.js is installed
echo 🔧 Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 14.x or higher
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js found: !NODE_VERSION!
)

REM Check if pnpm is installed
echo 🔧 Checking pnpm installation...
pnpm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  pnpm not found. Installing pnpm...
    npm install -g pnpm
    if !errorlevel! neq 0 (
        echo ❌ Failed to install pnpm
        pause
        exit /b 1
    ) else (
        echo ✅ pnpm installed successfully
    )
) else (
    for /f "tokens=*" %%i in ('pnpm --version') do set PNPM_VERSION=%%i
    echo ✅ pnpm found: !PNPM_VERSION!
)

REM Install dependencies
echo 🔧 Installing dependencies...
pnpm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
) else (
    echo ✅ Dependencies installed successfully
)

REM Setup environment file
echo 🔧 Setting up environment configuration...
if not exist .env (
    copy .env.example .env >nul
    echo ✅ Environment file created (.env)
    echo ⚠️  Please edit .env file with your configuration:
    echo.
    echo Required variables:
    echo   HELIUS_API_KEYS=key1,key2,key3,key4,key5
    echo   BOT_TOKEN=your_telegram_bot_token
    echo   ADMIN_CHAT_ID=your_telegram_chat_id
    echo   DATABASE_URL=your_database_connection_string
    echo.
) else (
    echo ℹ️  Environment file already exists (.env)
)

REM Create logs directory
echo 🔧 Creating logs directory...
if not exist logs mkdir logs
echo ✅ Logs directory created

REM Setup database (if configured)
echo 🔧 Setting up database...
findstr /C:"your_database_connection_string" .env >nul 2>&1
if %errorlevel% neq 0 (
    pnpm db:setup
    if !errorlevel! equ 0 (
        echo ✅ Database setup completed
    ) else (
        echo ⚠️  Database setup failed. Please check your DATABASE_URL
    )
) else (
    echo ℹ️  Skipping database setup (not configured)
)

REM Display setup summary
echo.
echo 🎉 Setup Summary
echo ================
echo.
echo 📦 Dependencies: ✅ Installed
echo ⚙️  Environment: ❌ Not configured (edit .env)
echo 🗄️  Database: ❌ Not configured (edit .env)
echo 📁 Logs: ✅ Ready
echo.

REM Next steps
echo 📋 Next Steps:
echo.
echo 1. 🔑 Get Helius API Keys:
echo    - Visit: https://helius.xyz
echo    - Get 3-5 API keys for best performance
echo.
echo 2. 🤖 Create Telegram Bot:
echo    - Message @BotFather on Telegram
echo    - Use /newbot command
echo    - Save your bot token
echo.
echo 3. ⚙️  Configure .env file:
echo    - Edit .env with your API keys and bot token
echo    - Set your database connection string
echo.
echo 4. 🚀 Start the bot:
echo    pnpm start
echo.

REM BotFather commands
echo 🤖 BotFather Commands Setup:
echo.
echo Send these commands to @BotFather:
echo.
echo /setcommands
echo Then paste:
echo.
echo start - 🏠 Open main menu
echo help - 🆘 Show all available commands
echo add - ➕ Add a single wallet to track
echo delete - ➖ Remove a single wallet
echo bulk_add - 📦 Add multiple wallets at once (up to 100)
echo bulk_remove - 🗑️ Remove multiple wallets at once
echo list_wallets - 📊 View your tracked wallets
echo manage - ⚙️ Manage all your tracked wallets
echo health - 🏥 System health status (admin only)
echo errors - 🚨 Error summary (admin only)
echo logs - 📄 View recent logs (admin only)
echo monitoring_config - ⚙️ View logging config (admin only)
echo cleanup_stats - 🧹 Cleanup statistics (admin only)
echo ban_wallet - 🚫 Ban a wallet (admin only)
echo upgrade - 💎 Upgrade subscription (optional)
echo help_notify - 📱 How notifications work
echo help_group - 👥 Add bot to groups
echo.

echo ✅ Setup completed! Your enhanced wallet tracker is ready.
echo.
echo 📚 Documentation:
echo   - README.md: Complete guide
echo   - QUICK-START-GUIDE.md: 5-minute setup
echo   - BULK-WALLET-GUIDE.md: Bulk operations
echo.
echo 🚀 Start tracking unlimited wallets with bulk operations!
echo.

pause
