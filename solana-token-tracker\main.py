import logging
import os
import threading
import requests
from flask import Flask, render_template, jsonify, request
from bot import run_bot

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)

logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)

# Global bot thread reference
bot_thread = None

@app.route('/')
def index():
    """Home page route."""
    global bot_thread
    
    # Check if bot thread is running
    thread_status = "running" if bot_thread and bot_thread.is_alive() else "stopped"
    
    return jsonify({
        "status": thread_status, 
        "message": f"Solana Token Price Tracker Bot is {thread_status}"
    })

@app.route('/check-updates')
def check_updates():
    """Check for Telegram updates."""
    try:
        token = os.environ.get("TELEGRAM_BOT_TOKEN")
        if not token:
            return jsonify({"error": "Telegram bot token not set"})
            
        url = f"https://api.telegram.org/bot{token}/getUpdates"
        response = requests.get(url)
        
        if response.status_code != 200:
            return jsonify({"error": f"Failed to get updates: {response.text}"})
            
        updates = response.json()
        
        if not updates.get("ok"):
            return jsonify({"error": f"Failed to get updates: {updates}"})
            
        return jsonify(updates)
    except Exception as e:
        return jsonify({"error": f"Exception: {str(e)}"})

@app.route('/start-bot')
def start_bot_endpoint():
    """Start the bot if it's not already running."""
    global bot_thread
    
    if bot_thread and bot_thread.is_alive():
        return jsonify({"status": "already_running", "message": "Bot is already running"})
    
    # Start the bot in a new thread
    bot_thread = threading.Thread(target=start_bot_in_thread)
    bot_thread.daemon = True
    bot_thread.start()
    
    return jsonify({"status": "started", "message": "Bot started successfully"})

def start_bot_in_thread():
    """Start the bot in a separate thread."""
    try:
        logger.info("Starting Solana Token Price Tracker Bot in thread")
        run_bot()
    except Exception as e:
        logger.error(f"Error in bot thread: {str(e)}")

# Don't automatically start the bot when the Flask app starts
# This helps prevent multiple instances running simultaneously
# The bot can be started via the /start-bot endpoint if needed
bot_thread = None

if __name__ == "__main__":
    # This entrypoint is for running standalone (not via flask/gunicorn)
    logger.info("Starting Solana Token Price Tracker Bot standalone")
    # Don't run the bot directly if this file is executed directly - we're using the flask app approach
    # run_bot()
    logger.info("Bot will be started by the Flask application.")
