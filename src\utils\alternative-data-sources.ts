import { logger } from '../services/logger-service'

// Alternative data sources for token information (inspired by solana-token-tracker)

// DexScreener API endpoints in order of preference
const DEXSCREENER_ENDPOINTS = [
  'https://api.dexscreener.com/latest/dex/tokens/',  // Primary endpoint - most reliable
  'https://api.dexscreener.io/latest/dex/tokens/',   // Alternative endpoint 1
  'https://api.dexscreener.com/v1/dex/tokens/',      // Alternative endpoint 2
  'https://api.dexscreener.io/v1/dex/tokens/'        // Alternative endpoint 3
]

// GeckoTerminal API endpoints
const GECKOTERMINAL_ENDPOINTS = [
  'https://api.geckoterminal.com/api/v2/networks/solana/tokens/',
  'https://api.geckoterminal.com/api/v2/networks/solana/pools/'
]

interface TokenInfo {
  success: boolean
  data?: {
    address: string
    name: string
    symbol: string
    price: number
  }
  error?: string
}

/**
 * Get token information from DexScreener API
 */
export async function getTokenInfoFromDexScreener(tokenAddress: string): Promise<TokenInfo> {
  logger.debug(`Fetching token info from DexScreener for: ${tokenAddress}`)

  for (const endpoint of DEXSCREENER_ENDPOINTS) {
    try {
      const url = `${endpoint}${tokenAddress}`
      logger.debug(`Trying DexScreener endpoint: ${endpoint}`)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Handi-Cat-Wallet-Tracker/1.0'
        }
      })

      if (!response.ok) {
        logger.warn(`DexScreener endpoint ${endpoint} returned ${response.status}`)
        continue
      }

      const data = await response.json()

      if (data.pairs && data.pairs.length > 0) {
        const pair = data.pairs[0]
        
        return {
          success: true,
          data: {
            address: tokenAddress,
            name: pair.baseToken?.name || `Token ${tokenAddress.substring(0, 6)}...`,
            symbol: pair.baseToken?.symbol || 'UNKNOWN',
            price: parseFloat(pair.priceUsd || '0')
          }
        }
      }
    } catch (error) {
      logger.error(`Error fetching from DexScreener endpoint ${endpoint}: ${error}`)
      continue
    }
  }

  return {
    success: false,
    error: 'Failed to fetch token info from all DexScreener endpoints'
  }
}

/**
 * Get token information from GeckoTerminal API
 */
export async function getTokenInfoFromGeckoTerminal(tokenAddress: string): Promise<TokenInfo> {
  logger.debug(`Fetching token info from GeckoTerminal for: ${tokenAddress}`)

  for (const endpoint of GECKOTERMINAL_ENDPOINTS) {
    try {
      const url = `${endpoint}${tokenAddress}`
      logger.debug(`Trying GeckoTerminal endpoint: ${endpoint}`)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Handi-Cat-Wallet-Tracker/1.0'
        }
      })

      if (!response.ok) {
        logger.warn(`GeckoTerminal endpoint ${endpoint} returned ${response.status}`)
        continue
      }

      const data = await response.json()

      if (data.data) {
        const tokenData = data.data
        
        return {
          success: true,
          data: {
            address: tokenAddress,
            name: tokenData.attributes?.name || `Token ${tokenAddress.substring(0, 6)}...`,
            symbol: tokenData.attributes?.symbol || 'UNKNOWN',
            price: 0 // GeckoTerminal requires additional pool lookup for price
          }
        }
      }
    } catch (error) {
      logger.error(`Error fetching from GeckoTerminal endpoint ${endpoint}: ${error}`)
      continue
    }
  }

  return {
    success: false,
    error: 'Failed to fetch token info from all GeckoTerminal endpoints'
  }
}

/**
 * Get token information with fallback to multiple sources
 */
export async function getTokenInfoWithFallback(tokenAddress: string): Promise<TokenInfo> {
  logger.info(`Getting token info with fallback for: ${tokenAddress}`)

  // Try DexScreener first (most reliable for price data)
  const dexScreenerResult = await getTokenInfoFromDexScreener(tokenAddress)
  if (dexScreenerResult.success && dexScreenerResult.data?.price > 0) {
    logger.info(`Successfully got token info from DexScreener: ${dexScreenerResult.data.name}`)
    return dexScreenerResult
  }

  // Try GeckoTerminal as fallback
  const geckoTerminalResult = await getTokenInfoFromGeckoTerminal(tokenAddress)
  if (geckoTerminalResult.success) {
    logger.info(`Successfully got token info from GeckoTerminal: ${geckoTerminalResult.data?.name}`)
    return geckoTerminalResult
  }

  // If all fail, return a default response
  logger.warn(`All fallback sources failed for token: ${tokenAddress}`)
  return {
    success: true,
    data: {
      address: tokenAddress,
      name: `Token ${tokenAddress.substring(0, 6)}...${tokenAddress.substring(-4)}`,
      symbol: 'UNKNOWN',
      price: 0
    }
  }
}

/**
 * Validate Solana address format
 */
export function isValidSolanaAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false
  }

  // Check if this is a PumpFun token (ends with "pump")
  if (address.toLowerCase().endsWith('pump')) {
    const baseAddress = address.slice(0, -4) // Remove "pump" suffix
    if (baseAddress.length < 25 || baseAddress.length > 50) {
      return false
    }
    // Check if it contains only base58 characters
    const base58Pattern = /^[1-9A-HJ-NP-Za-km-z]+$/
    return base58Pattern.test(baseAddress)
  }

  // Standard Solana address check
  if (address.length < 32 || address.length > 44) {
    return false
  }

  // Check if it contains only base58 characters (alphanumeric without 0, O, I, l)
  const base58Pattern = /^[1-9A-HJ-NP-Za-km-z]+$/
  return base58Pattern.test(address)
}
