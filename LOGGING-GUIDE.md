# 📊 Comprehensive Logging & Error Tracking Guide

This guide explains the advanced logging and error tracking system implemented for unlimited wallet tracking.

## 🎯 **What's Included**

✅ **Complete Error Tracking** - Every error logged with context
✅ **Performance Monitoring** - Track slow operations and bottlenecks  
✅ **API Call Logging** - Monitor He<PERSON> and <PERSON><PERSON> calls
✅ **File-based Logging** - Persistent logs with rotation
✅ **Real-time Monitoring** - Live system health tracking
✅ **Admin Commands** - Monitor and debug via Telegram
✅ **Automatic Reports** - Daily and hourly summaries

## 🔧 **Configuration**

### **Basic Setup (.env)**

```env
# 📊 LOGGING CONFIGURATION
LOG_LEVEL=debug                    # error, warn, info, debug, verbose
ENABLE_FILE_LOGGING=true          # Save logs to files
LOG_DIRECTORY=logs                # Log file directory
MAX_LOG_FILE_SIZE=50              # MB before rotation
MAX_LOG_FILES=10                  # Number of files to keep

# 🔍 ERROR TRACKING
ENABLE_ERROR_TRACKING=true        # Track all errors with context
TRACK_API_ERRORS=true             # Track Helius/RPC failures
TRACK_CONNECTION_ERRORS=true      # Track connection issues
TRACK_TRANSACTION_ERRORS=true     # Track parsing errors

# 📈 PERFORMANCE MONITORING
ENABLE_PERFORMANCE_LOGGING=true   # Monitor operation speed
SLOW_OPERATION_THRESHOLD=5000     # Log operations slower than 5s

# 🚨 ALERTS
ENABLE_CONSOLE_ALERTS=true        # Show critical errors in console
ENABLE_FILE_ALERTS=true           # Save critical errors to files

# 🌐 API LOGGING
LOG_HELIUS_CALLS=true             # Log all Helius API calls
LOG_RPC_CALLS=false               # Log RPC calls (can be verbose)
LOG_RATE_LIMITS=true              # Log rate limit events

# 📊 STATISTICS
ENABLE_STATS_LOGGING=true         # Hourly system statistics
ENABLE_DAILY_REPORTS=true         # Daily summary reports
```

## 📁 **Log Files Structure**

When `ENABLE_FILE_LOGGING=true`, logs are saved to:

```
logs/
├── wallet-tracker-2024-01-15.log     # General application logs
├── errors-2024-01-15.log             # Error-only logs
├── error-details-2024-01-15.log      # Detailed error tracking
├── performance-2024-01-15.log        # Performance metrics
└── daily-report-2024-01-15.json      # Daily summary report
```

## 🎛️ **Admin Commands**

### **System Health**
```
/health
```
Shows real-time system status:
- RPC connection health
- Helius API key availability  
- Active wallet count
- Memory usage
- Error statistics

### **Error Summary**
```
/errors
```
Displays recent errors by component:
- Error counts and types
- Last occurrence times
- Severity levels
- Component breakdown

### **View Logs**
```
/logs
```
Shows recent log files and displays last 50 error entries.

### **Configuration**
```
/monitoring_config
```
Displays current logging configuration and available options.

### **Test Monitoring**
```
/monitoring_test
```
Runs test logs to verify all systems are working.

## 🔍 **What Gets Logged**

### **Wallet Events**
```
✅ Wallet tracking started/stopped
✅ Subscription creation/failure
✅ Connection assignments
✅ Rate limiting events
```

### **Transaction Events**
```
✅ Transaction detection
✅ Parsing success/failure
✅ API call performance
✅ Error details with context
```

### **API Monitoring**
```
✅ Helius API calls (success/failure)
✅ RPC endpoint performance
✅ Rate limit detection
✅ Failover events
```

### **System Health**
```
✅ Memory usage tracking
✅ Connection statistics
✅ Error rate monitoring
✅ Performance metrics
```

## 📊 **Log Levels**

### **ERROR** - Critical issues
```
❌ API failures
❌ Database errors
❌ Connection failures
❌ Transaction parsing errors
```

### **WARN** - Important notices
```
⚠️ Rate limits hit
⚠️ Slow operations
⚠️ Failover events
⚠️ High memory usage
```

### **INFO** - General information
```
ℹ️ Wallet tracking events
ℹ️ System startup/shutdown
ℹ️ Configuration changes
ℹ️ Statistics summaries
```

### **DEBUG** - Detailed debugging
```
🔍 Connection selections
🔍 API call details
🔍 Performance timings
🔍 Internal state changes
```

### **VERBOSE** - Everything
```
📝 All API calls
📝 All database queries
📝 All internal operations
📝 Detailed stack traces
```

## 🚨 **Error Tracking Examples**

### **Helius API Failure**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "error",
  "message": "Helius API call failed: /transactions",
  "component": "helius-api",
  "apiKeyIndex": 2,
  "endpoint": "/transactions",
  "error": {
    "message": "Rate limit exceeded",
    "code": "RATE_LIMIT_ERROR"
  },
  "duration": 5000
}
```

### **Wallet Connection Error**
```json
{
  "timestamp": "2024-01-15T10:31:00Z",
  "level": "error", 
  "message": "Failed to create subscription for wallet",
  "component": "wallet-subscription",
  "walletAddress": "ABC123...",
  "attempt": 3,
  "maxRetries": 3,
  "error": {
    "message": "WebSocket connection failed",
    "stack": "Error: WebSocket connection failed..."
  }
}
```

### **Performance Issue**
```json
{
  "timestamp": "2024-01-15T10:32:00Z",
  "level": "warn",
  "message": "getParsedTransaction slow (8500ms)",
  "component": "transaction-fetcher",
  "duration": 8500,
  "transactionSignature": "XYZ789...",
  "isSlowOperation": true
}
```

## 📈 **Monitoring Dashboard**

### **Real-time Health Check**
```
🏥 System Health Summary

🔗 RPC Connections: 5/5 healthy
🔑 Helius Keys: 4/5 available  
👛 Wallets: 150 active, 5 banned
💾 Memory: 65% used
⏱️ Uptime: 24 hours
❌ Errors: 3 last hour, 0 critical
```

### **Error Summary**
```
🚨 Error Summary

helius-api (medium)
• Errors: 15
• Last: 2024-01-15 10:30:00
• Types: rate_limit:10, timeout:5

wallet-subscription (low)  
• Errors: 3
• Last: 2024-01-15 09:45:00
• Types: connection:2, timeout:1
```

## 🛠️ **Troubleshooting**

### **High Error Rate**
1. Check `/errors` for component breakdown
2. Review `/logs` for detailed error messages
3. Check `/health` for system status
4. Verify API key validity and limits

### **Performance Issues**
1. Check memory usage in `/health`
2. Review performance logs for slow operations
3. Monitor API call durations
4. Check for rate limiting events

### **Connection Problems**
1. Verify RPC endpoints are accessible
2. Check Helius API key status
3. Review connection error logs
4. Test with `/monitoring_test`

## 🔧 **Customization**

### **Adjust Log Levels**
```env
# For production (less verbose)
LOG_LEVEL=warn

# For debugging (very verbose)
LOG_LEVEL=debug

# For development (everything)
LOG_LEVEL=verbose
```

### **Focus on Specific Components**
```env
# Only log API errors
TRACK_API_ERRORS=true
TRACK_CONNECTION_ERRORS=false
TRACK_TRANSACTION_ERRORS=false

# Only log Helius calls
LOG_HELIUS_CALLS=true
LOG_RPC_CALLS=false
```

### **Reduce Log Volume**
```env
# Disable file logging
ENABLE_FILE_LOGGING=false

# Reduce performance logging
ENABLE_PERFORMANCE_LOGGING=false
SLOW_OPERATION_THRESHOLD=10000

# Disable statistics
ENABLE_STATS_LOGGING=false
ENABLE_DAILY_REPORTS=false
```

## 📋 **Best Practices**

### **For Development**
```env
LOG_LEVEL=debug
ENABLE_FILE_LOGGING=true
ENABLE_ERROR_TRACKING=true
LOG_HELIUS_CALLS=true
ENABLE_PERFORMANCE_LOGGING=true
```

### **For Production**
```env
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true
ENABLE_ERROR_TRACKING=true
LOG_HELIUS_CALLS=false
LOG_RPC_CALLS=false
ENABLE_DAILY_REPORTS=true
```

### **For Debugging Issues**
```env
LOG_LEVEL=verbose
TRACK_API_ERRORS=true
TRACK_CONNECTION_ERRORS=true
TRACK_TRANSACTION_ERRORS=true
LOG_HELIUS_CALLS=true
LOG_RPC_CALLS=true
```

## 🎯 **Summary**

With this comprehensive logging system, you can:

✅ **Track every error** with full context and stack traces
✅ **Monitor performance** and identify bottlenecks
✅ **Debug API issues** with detailed call logging
✅ **Get real-time health** status via Telegram commands
✅ **Receive automatic reports** on system performance
✅ **Troubleshoot issues** quickly with detailed logs

**All errors, performance issues, and system events are now fully tracked and logged for easy debugging and monitoring!** 🚀
