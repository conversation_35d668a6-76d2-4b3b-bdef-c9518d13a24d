import { Connection, clusterApiUrl } from '@solana/web3.js'
import chalk from 'chalk'
import dotenv from 'dotenv'
import { logger } from '../services/logger-service'

dotenv.config()

// Enhanced Helius API key management (inspired by solana-token-tracker)
const HELIUS_API_KEYS: string[] = []

// Load API keys from multiple sources
function loadHeliusApiKeys(): void {
  HELIUS_API_KEYS.length = 0 // Clear existing keys

  // Load from comma-separated HELIUS_API_KEYS
  const keysFromEnv = process.env.HELIUS_API_KEYS?.split(',')
    .map((key) => key.trim())
    .filter(Boolean) ?? []

  HELIUS_API_KEYS.push(...keysFromEnv)

  // Load additional keys (HELIUS_API_KEY_1 to HELIUS_API_KEY_10)
  for (let i = 1; i <= 10; i++) {
    const keyName = `HELIUS_API_KEY_${i}`
    const additionalKey = process.env[keyName]
    if (additionalKey && !HELIUS_API_KEYS.includes(additionalKey)) {
      HELIUS_API_KEYS.push(additionalKey)
    }
  }

  // Fallback to single key for backward compatibility
  if (HELIUS_API_KEYS.length === 0 && process.env.HELIUS_API_KEY) {
    HELIUS_API_KEYS.push(process.env.HELIUS_API_KEY)
  }

  logger.info(`Loaded ${HELIUS_API_KEYS.length} Helius API keys`)
}

// Initialize API keys
loadHeliusApiKeys()

const RPC_ENDPOINTS =
  process.env.RPC_ENDPOINTS?.split(',')
    .map((url) => url.trim())
    .filter(Boolean) ?? []

console.log(chalk.bold.greenBright(`LOADED ${RPC_ENDPOINTS.length} RPC ENDPOINTS`))
console.log(chalk.bold.blueBright(`LOADED ${HELIUS_API_KEYS.length} HELIUS API KEYS`))

// Log configuration details
logger.info('Solana provider initialized', {
  component: 'solana-provider',
  metadata: {
    rpcEndpointsCount: RPC_ENDPOINTS.length,
    heliusKeysCount: HELIUS_API_KEYS.length,
    rpcEndpoints: RPC_ENDPOINTS.map(url => url.split('?')[0]), // Hide API keys in logs
  }
})

// Performance tracking for RPC endpoints
interface RpcPerformanceMetrics {
  endpoint: string
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  lastUsed: Date
  isHealthy: boolean
}

// API key status tracking (inspired by solana-token-tracker)
interface ApiKeyStatus {
  requests: number
  errors: number
  rateLimited: boolean
  lastUsed: Date
  lastError?: {
    type: string
    message: string
    time: Date
  }
}

// Enhanced RPC Connection Manager with multiple Helius API key support
export class RpcConnectionManager {
  static connections: Connection[] = RPC_ENDPOINTS.map((url) => new Connection(url, 'confirmed'))

  // Multiple Helius connections for load balancing
  static heliusConnections: Connection[] = HELIUS_API_KEYS.map(
    (key) => new Connection(`https://mainnet.helius-rpc.com/?api-key=${key}`, 'confirmed')
  )

  // Current index for round-robin rotation
  private static currentHeliusIndex = 0
  private static failedKeys = new Set<number>()

  // Performance tracking
  private static rpcMetrics = new Map<string, RpcPerformanceMetrics>()
  private static heliusMetrics = new Map<string, RpcPerformanceMetrics>()

  // API key status tracking
  private static apiKeyStatus = new Map<string, ApiKeyStatus>()

  // Backward compatibility - use first Helius connection as default
  static logConnection = RpcConnectionManager.heliusConnections[0] || new Connection('https://api.mainnet-beta.solana.com', 'confirmed')

  static getRandomConnection(): Connection {
    if (RpcConnectionManager.connections.length === 0) {
      logger.warn('No RPC endpoints configured, using default Solana RPC', {
        component: 'rpc-connection-manager'
      })
      console.warn('No RPC endpoints configured, using default Solana RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'confirmed')
    }
    const randomIndex = Math.floor(Math.random() * RpcConnectionManager.connections.length)

    logger.debug('Selected random RPC connection', {
      component: 'rpc-connection-manager',
      metadata: { connectionIndex: randomIndex, totalConnections: RpcConnectionManager.connections.length }
    })

    return RpcConnectionManager.connections[randomIndex]
  }

  /**
   * Get the next available Helius connection using round-robin with failover
   */
  static getNextHeliusConnection(): Connection {
    if (RpcConnectionManager.heliusConnections.length === 0) {
      console.warn('No Helius API keys configured, falling back to default RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'confirmed')
    }

    // If all keys have failed, reset the failed set and try again
    if (RpcConnectionManager.failedKeys.size >= RpcConnectionManager.heliusConnections.length) {
      console.log('All Helius keys failed, resetting failed keys set')
      RpcConnectionManager.failedKeys.clear()
    }

    // Find next available key
    let attempts = 0
    while (attempts < RpcConnectionManager.heliusConnections.length) {
      const connection = RpcConnectionManager.heliusConnections[RpcConnectionManager.currentHeliusIndex]

      // Move to next index for round-robin
      RpcConnectionManager.currentHeliusIndex =
        (RpcConnectionManager.currentHeliusIndex + 1) % RpcConnectionManager.heliusConnections.length

      // Check if this key has failed recently
      if (!RpcConnectionManager.failedKeys.has(RpcConnectionManager.currentHeliusIndex)) {
        return connection
      }

      attempts++
    }

    // If we get here, return the first connection as fallback
    console.warn('All Helius connections marked as failed, using first connection as fallback')
    return RpcConnectionManager.heliusConnections[0]
  }

  /**
   * Mark a Helius API key as failed
   */
  static markHeliusKeyAsFailed(connection: Connection): void {
    const index = RpcConnectionManager.heliusConnections.indexOf(connection)
    if (index !== -1) {
      RpcConnectionManager.failedKeys.add(index)

      logger.warn('Helius API key marked as failed', {
        component: 'helius-connection-manager',
        metadata: {
          keyIndex: index,
          totalKeys: RpcConnectionManager.heliusConnections.length,
          failedKeysCount: RpcConnectionManager.failedKeys.size
        }
      })

      console.warn(`Marked Helius key at index ${index} as failed`)

      // Auto-recover failed keys after 5 minutes
      setTimeout(() => {
        RpcConnectionManager.failedKeys.delete(index)

        logger.info('Helius API key recovered', {
          component: 'helius-connection-manager',
          metadata: {
            keyIndex: index,
            failedKeysCount: RpcConnectionManager.failedKeys.size
          }
        })

        console.log(`Recovered Helius key at index ${index}`)
      }, 5 * 60 * 1000)
    }
  }

  /**
   * Get a random Helius connection for load balancing
   */
  static getRandomHeliusConnection(): Connection {
    if (RpcConnectionManager.heliusConnections.length === 0) {
      console.warn('No Helius API keys configured, falling back to default RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'confirmed')
    }

    const availableConnections = RpcConnectionManager.heliusConnections.filter(
      (_, index) => !RpcConnectionManager.failedKeys.has(index)
    )

    if (availableConnections.length === 0) {
      console.warn('No available Helius connections, using first connection as fallback')
      return RpcConnectionManager.heliusConnections[0]
    }

    const randomIndex = Math.floor(Math.random() * availableConnections.length)
    return availableConnections[randomIndex]
  }

  static resetLogConnection() {
    RpcConnectionManager.logConnection = RpcConnectionManager.getNextHeliusConnection()
  }

  /**
   * Initialize performance metrics for an endpoint
   */
  private static initializeMetrics(endpoint: string, isHelius: boolean = false): void {
    const metrics: RpcPerformanceMetrics = {
      endpoint: endpoint.split('?')[0], // Hide API keys
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastUsed: new Date(),
      isHealthy: true
    }

    if (isHelius) {
      RpcConnectionManager.heliusMetrics.set(endpoint, metrics)
    } else {
      RpcConnectionManager.rpcMetrics.set(endpoint, metrics)
    }
  }

  /**
   * Track RPC request performance
   */
  static trackRpcPerformance(endpoint: string, responseTime: number, success: boolean, isHelius: boolean = false): void {
    const metricsMap = isHelius ? RpcConnectionManager.heliusMetrics : RpcConnectionManager.rpcMetrics

    if (!metricsMap.has(endpoint)) {
      RpcConnectionManager.initializeMetrics(endpoint, isHelius)
    }

    const metrics = metricsMap.get(endpoint)!
    metrics.totalRequests++
    metrics.lastUsed = new Date()

    if (success) {
      metrics.successfulRequests++
    } else {
      metrics.failedRequests++
    }

    // Update average response time
    const totalResponseTime = metrics.averageResponseTime * (metrics.totalRequests - 1) + responseTime
    metrics.averageResponseTime = totalResponseTime / metrics.totalRequests

    // Update health status (healthy if success rate > 80%)
    metrics.isHealthy = (metrics.successfulRequests / metrics.totalRequests) > 0.8

    // Log performance if enabled
    if (process.env.LOG_RPC_CALLS === 'true') {
      logger.debug('RPC performance tracked', {
        component: 'rpc-performance',
        metadata: {
          endpoint: metrics.endpoint,
          responseTime,
          success,
          isHelius,
          successRate: (metrics.successfulRequests / metrics.totalRequests * 100).toFixed(2) + '%'
        }
      })
    }
  }

  /**
   * Get performance metrics for all endpoints
   */
  static getPerformanceMetrics() {
    return {
      rpcEndpoints: Array.from(RpcConnectionManager.rpcMetrics.values()),
      heliusEndpoints: Array.from(RpcConnectionManager.heliusMetrics.values())
    }
  }

  /**
   * Get the best performing RPC endpoint
   */
  static getBestPerformingEndpoint(): { endpoint: string; metrics: RpcPerformanceMetrics } | null {
    const allMetrics = [
      ...Array.from(RpcConnectionManager.rpcMetrics.values()),
      ...Array.from(RpcConnectionManager.heliusMetrics.values())
    ]

    const healthyEndpoints = allMetrics.filter(m => m.isHealthy && m.totalRequests > 0)

    if (healthyEndpoints.length === 0) return null

    // Sort by success rate first, then by response time
    healthyEndpoints.sort((a, b) => {
      const aSuccessRate = a.successfulRequests / a.totalRequests
      const bSuccessRate = b.successfulRequests / b.totalRequests

      if (aSuccessRate !== bSuccessRate) {
        return bSuccessRate - aSuccessRate // Higher success rate first
      }

      return a.averageResponseTime - b.averageResponseTime // Lower response time first
    })

    return {
      endpoint: healthyEndpoints[0].endpoint,
      metrics: healthyEndpoints[0]
    }
  }

  /**
   * Initialize API key status if not exists
   */
  private static initializeApiKeyStatus(apiKey: string): void {
    if (!RpcConnectionManager.apiKeyStatus.has(apiKey)) {
      RpcConnectionManager.apiKeyStatus.set(apiKey, {
        requests: 0,
        errors: 0,
        rateLimited: false,
        lastUsed: new Date()
      })
    }
  }

  /**
   * Get best available Helius API key (inspired by solana-token-tracker)
   */
  static getBestHeliusApiKey(): string | null {
    if (HELIUS_API_KEYS.length === 0) return null

    // Initialize status for all keys
    HELIUS_API_KEYS.forEach(key => RpcConnectionManager.initializeApiKeyStatus(key))

    // Filter out rate-limited keys if possible
    const availableKeys = HELIUS_API_KEYS.filter(key => {
      const status = RpcConnectionManager.apiKeyStatus.get(key)
      return status && !status.rateLimited
    })

    // If all keys are rate-limited, use all keys
    const keysToUse = availableKeys.length > 0 ? availableKeys : HELIUS_API_KEYS

    // Select random key from available keys for load balancing
    const selectedKey = keysToUse[Math.floor(Math.random() * keysToUse.length)]

    // Update last used time
    const status = RpcConnectionManager.apiKeyStatus.get(selectedKey)
    if (status) {
      status.lastUsed = new Date()
    }

    return selectedKey
  }

  /**
   * Handle API key error (inspired by solana-token-tracker)
   */
  static handleApiKeyError(apiKey: string, errorType: string, errorMessage: string): void {
    RpcConnectionManager.initializeApiKeyStatus(apiKey)

    const status = RpcConnectionManager.apiKeyStatus.get(apiKey)!
    status.errors++
    status.lastError = {
      type: errorType,
      message: errorMessage,
      time: new Date()
    }

    // Check if this is a rate limit error
    const isRateLimit = (
      errorMessage.toLowerCase().includes('rate limit') ||
      errorMessage.toLowerCase().includes('too many requests') ||
      errorMessage.includes('429') ||
      errorMessage.includes('-32401') // Helius batch request error
    )

    if (isRateLimit) {
      status.rateLimited = true
      logger.warn(`API key ${apiKey.substring(0, 4)}... has been rate limited: ${errorMessage}`)
    } else {
      logger.error(`API key ${apiKey.substring(0, 4)}... error: ${errorType} - ${errorMessage}`)
    }
  }

  /**
   * Update API key request count
   */
  static updateApiKeyRequestCount(apiKey: string): void {
    RpcConnectionManager.initializeApiKeyStatus(apiKey)

    const status = RpcConnectionManager.apiKeyStatus.get(apiKey)!
    status.requests++
    status.lastUsed = new Date()
  }

  /**
   * Get API key status for all keys
   */
  static getApiKeyStatus(): Record<string, any> {
    const status: Record<string, any> = {}

    HELIUS_API_KEYS.forEach((key, index) => {
      const keyId = `HELIUS_API_KEY${index === 0 ? '' : `_${index + 1}`}`
      const keyStatus = RpcConnectionManager.apiKeyStatus.get(key)

      status[keyId] = {
        keyPreview: `${key.substring(0, 4)}...`,
        requests: keyStatus?.requests || 0,
        errors: keyStatus?.errors || 0,
        rateLimited: keyStatus?.rateLimited || false,
        lastUsed: keyStatus?.lastUsed || new Date(),
        lastError: keyStatus?.lastError || null
      }
    })

    return status
  }

  /**
   * Get connection statistics
   */
  static getConnectionStats() {
    const performanceMetrics = RpcConnectionManager.getPerformanceMetrics()
    const bestEndpoint = RpcConnectionManager.getBestPerformingEndpoint()
    const apiKeyStatus = RpcConnectionManager.getApiKeyStatus()

    return {
      totalRpcEndpoints: RpcConnectionManager.connections.length,
      totalHeliusKeys: RpcConnectionManager.heliusConnections.length,
      failedHeliusKeys: RpcConnectionManager.failedKeys.size,
      availableHeliusKeys: RpcConnectionManager.heliusConnections.length - RpcConnectionManager.failedKeys.size,
      performanceMetrics,
      bestPerformingEndpoint: bestEndpoint,
      apiKeyStatus
    }
  }
}
