{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"<PERSON>rro<PERSON> tracked Unhandled Promise Rejection","severity":"critical","stack":"TypeError: allWallets is not iterable (cannot read property undefined)\n    at TrackWallets.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\dist\\src\\lib\\track-wallets.js:150:97)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\dist\\src\\lib\\track-wallets.js:5:58)","timestamp":"2025-05-29 13:05:47"}
{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"<PERSON><PERSON>r tracked Unhandled Promise Rejection","severity":"critical","stack":"PrismaClientInitializationError: \nInvalid `prisma_1.default.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\dist\\src\\repositories\\prisma\\user.js:40:54\n\n  37 }\n  38 getById(userId) {\n  39     return __awaiter(this, void 0, void 0, function* () {\n→ 40         const user = yield prisma_1.default.user.findUnique(\nError in connector: Error creating a database connection. (Kind: An error occurred during DNS resolution: no record found for Query { name: Name(\"_mongodb._tcp.cluster0.s8spl.mongodb.net.\"), query_type: SRV, query_class: IN }, labels: {})\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@prisma+client@5.22.0_prisma@5.22.0\\node_modules\\@prisma\\client\\runtime\\library.js:121:7615)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@prisma+client@5.22.0_prisma@5.22.0\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@prisma+client@5.22.0_prisma@5.22.0\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\@prisma+client@5.22.0_prisma@5.22.0\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)","timestamp":"2025-05-29 13:06:30"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 94.20344112351324%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 13:15:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 95.53648416912107%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 13:25:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 96.1216669689824%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 13:45:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 96.49158934014703%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 13:50:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 95.10337184580811%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 14:15:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 95.53273384913668%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main1\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 14:20:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 96.62264538035295%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 15:35:00"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 94.6150203962704%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main2\\node_modules\\.pnpm\\node-cron@3.0.3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 15:40:00"}
