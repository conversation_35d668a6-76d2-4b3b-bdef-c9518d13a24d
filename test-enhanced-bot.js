// Enhanced Handi Cat Wallet Tracker - Interface Test Version
require('dotenv').config()
const TelegramBot = require('node-telegram-bot-api')

const BOT_TOKEN = process.env.BOT_TOKEN

console.log('🚀 Starting Enhanced Handi Cat Wallet Tracker - Interface Test')
console.log('Bot Token:', BOT_TOKEN ? 'Found' : 'Missing')

if (!BOT_TOKEN) {
  console.error('❌ BOT_TOKEN not found in .env file')
  process.exit(1)
}

// Create bot with polling
const bot = new TelegramBot(BOT_TOKEN, {
  polling: {
    interval: 1000,
    autoStart: true,
    params: {
      timeout: 10,
    }
  }
})

console.log('✅ Enhanced bot created with polling mode')

// Enhanced Start Menu
const START_MENU = {
  inline_keyboard: [
    // 🔥 ENHANCED BULK OPERATIONS (NEW!)
    [
      { text: '📦 Bulk Add Wallets', callback_data: 'bulk_add' },
      { text: '🗑️ Bulk Remove', callback_data: 'bulk_remove' },
    ],
    // 📊 WALLET MANAGEMENT
    [
      { text: '➕ Add Single Wallet', callback_data: 'add' },
      { text: '📊 List My Wallets', callback_data: 'list_wallets' },
    ],
    [
      { text: '👀 Manage Wallets', callback_data: 'manage' },
      { text: '🗑️ Delete Wallet', callback_data: 'delete' },
    ],
    // 🔧 SYSTEM & SETTINGS
    [
      { text: '👛 My Wallet', callback_data: 'my_wallet' },
      { text: '⚙️ Settings', callback_data: 'settings' },
    ],
    [
      { text: '🆕 Groups', callback_data: 'groups' },
      { text: '🔎 Help', callback_data: 'help' },
    ],
    // 📊 ADMIN & MONITORING (if admin)
    [
      { text: '🏥 System Health', callback_data: 'health' },
      { text: '🚨 Error Monitor', callback_data: 'errors' },
    ],
    // 💰 OPTIONAL FEATURES
    [
      { text: '❤️ Donate', callback_data: 'donate' },
      { text: '👑 Upgrade', callback_data: 'upgrade' },
    ],
  ],
}

// Enhanced start message
function getStartMessage(userWalletCount = 0) {
  return `
🐱 <b>Handi Cat Wallet Tracker - Enhanced Edition</b>

🚀 <b>UNLIMITED WALLET TRACKING NOW AVAILABLE!</b>

📊 <b>Your Status:</b>
• Currently tracking: <b>${userWalletCount} wallets</b>
• Limit: <b>UNLIMITED</b> ✨
• Status: <b>Enhanced Edition Active</b> 🔥

🔥 <b>NEW ENHANCED FEATURES:</b>
📦 <b>Bulk Operations</b> - Add/remove up to 100 wallets at once
⚡ <b>Multiple API Keys</b> - Smart load balancing & failover
🛡️ <b>Auto Cleanup</b> - Removes inactive wallets after 5 days
📊 <b>Advanced Monitoring</b> - Real-time health & error tracking
🔄 <b>Smart Retry</b> - Automatic retry with exponential backoff

💡 <b>Quick Start:</b>
• 📦 <b>Bulk Add:</b> Add multiple wallets instantly
• ➕ <b>Single Add:</b> Add one wallet at a time
• 📊 <b>List Wallets:</b> View all your tracked wallets
• 👀 <b>Manage:</b> Full wallet management suite

🎯 <b>Perfect for:</b>
• 🐋 Whale watching with unlimited wallets
• 📈 DeFi protocol monitoring
• 🎨 NFT collector tracking
• 💼 Portfolio management

<i>🎉 Enhanced with bulk operations and unlimited tracking!</i>
  `
}

// Start command
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id
  const userName = msg.from.first_name || 'User'

  console.log(`📱 Received /start from ${userName} (${chatId})`)

  const startMessage = getStartMessage(0)

  bot.sendMessage(chatId, startMessage, {
    parse_mode: 'HTML',
    reply_markup: START_MENU
  })
})

// Callback query handler
bot.on('callback_query', (callbackQuery) => {
  const message = callbackQuery.message
  const data = callbackQuery.data
  const chatId = message.chat.id
  const userName = callbackQuery.from.first_name || 'User'

  console.log(`🔘 Button clicked: ${data} by ${userName}`)

  // Answer the callback query to remove loading state
  bot.answerCallbackQuery(callbackQuery.id)

  switch (data) {
    case 'bulk_add':
      const bulkAddMessage = `
📦 <b>Bulk Add Wallets - Enhanced Edition</b>

🚀 <b>Add up to 100 wallets at once!</b>

<b>📝 Supported formats:</b>
• <code>wallet_address</code>
• <code>wallet_address wallet_name</code>
• <code>wallet_address,wallet_name</code>

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector</code>

💡 <b>Features:</b>
• ✅ Real-time progress tracking
• ✅ Smart validation & duplicate detection
• ✅ Detailed success/failure reporting
• ✅ Unlimited wallet tracking

<b>Send your wallet list now or use /bulk_add command:</b>
      `
      bot.sendMessage(chatId, bulkAddMessage, { parse_mode: 'HTML' })
      break

    case 'bulk_remove':
      const bulkRemoveMessage = `
🗑️ <b>Bulk Remove Wallets - Enhanced Edition</b>

🚀 <b>Remove multiple wallets quickly!</b>

<b>📝 Format:</b>
Send wallet addresses, one per line:

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S</code>

💡 <b>Features:</b>
• ✅ Remove up to 100 wallets at once
• ✅ Progress tracking with live updates
• ✅ Only removes wallets you're tracking
• ✅ Detailed removal summary

<b>Send your wallet list now or use /bulk_remove command:</b>
      `
      bot.sendMessage(chatId, bulkRemoveMessage, { parse_mode: 'HTML' })
      break

    case 'list_wallets':
      const listMessage = `
📊 <b>Your Tracked Wallets - Enhanced Edition</b>

👛 <b>Total wallets:</b> 0 (Demo Mode)
🚀 <b>Status:</b> Unlimited tracking enabled!

💡 <b>Available operations:</b>
• 📦 <b>/bulk_add</b> - Add multiple wallets (up to 100)
• 🗑️ <b>/bulk_remove</b> - Remove multiple wallets
• ➕ <b>/add</b> - Add single wallet
• 🗑️ <b>/delete</b> - Remove single wallet
• 👀 <b>/manage</b> - Manage all wallets

🔥 <b>Enhanced Features Active:</b>
✅ Multiple Helius API keys for reliability
✅ Smart load balancing & failover
✅ Automatic cleanup after 5 days inactivity
✅ Real-time transaction notifications
✅ Comprehensive error tracking & monitoring

<i>🎉 Unlimited wallet tracking with bulk operations!</i>
      `
      bot.sendMessage(chatId, listMessage, { parse_mode: 'HTML' })
      break

    case 'health':
      const healthMessage = `
🏥 <b>System Health - Enhanced Edition</b>

✅ <b>Bot Status:</b> Online and operational
✅ <b>Database:</b> Connected (SQLite)
✅ <b>API Keys:</b> 2 Helius keys loaded
✅ <b>RPC Endpoints:</b> 2 endpoints available
✅ <b>Memory Usage:</b> Normal
✅ <b>Error Rate:</b> Low

🔥 <b>Enhanced Features:</b>
• Load balancing across multiple endpoints
• Automatic failover protection
• Real-time error monitoring
• Smart retry mechanisms

<i>All systems operational! 🚀</i>
      `
      bot.sendMessage(chatId, healthMessage, { parse_mode: 'HTML' })
      break

    case 'add':
      const addMessage = `
➕ <b>Add Single Wallet - Enhanced Edition</b>

🚀 <b>Add one wallet to track!</b>

<b>📝 How to add:</b>
1. Send me a Solana wallet address
2. Optionally include a name: <code>wallet_address Wallet Name</code>

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1</code>
or
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 My Whale Wallet</code>

💡 <b>Features:</b>
• ✅ Instant validation
• ✅ Real-time notifications
• ✅ Unlimited tracking
• ✅ Auto cleanup after 5 days

<b>Send your wallet address now:</b>
      `
      bot.sendMessage(chatId, addMessage, { parse_mode: 'HTML' })
      break

    case 'delete':
      const deleteMessage = `
🗑️ <b>Delete Single Wallet - Enhanced Edition</b>

🚀 <b>Remove one wallet from tracking!</b>

<b>📝 How to delete:</b>
1. Send me the wallet address to remove
2. Or use the wallet name if you assigned one

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1</code>
or
<code>My Whale Wallet</code>

💡 <b>Features:</b>
• ✅ Safe removal with confirmation
• ✅ Only removes your tracked wallets
• ✅ Instant feedback

<b>Send the wallet address or name to delete:</b>
      `
      bot.sendMessage(chatId, deleteMessage, { parse_mode: 'HTML' })
      break

    case 'manage':
      const manageMessage = `
👀 <b>Manage Wallets - Enhanced Edition</b>

🚀 <b>Full wallet management suite!</b>

<b>📊 Available Operations:</b>
• 📦 <b>Bulk Add:</b> Add up to 100 wallets at once
• 🗑️ <b>Bulk Remove:</b> Remove multiple wallets
• ➕ <b>Single Add:</b> Add one wallet
• 🗑️ <b>Single Delete:</b> Remove one wallet
• 📊 <b>List All:</b> View all tracked wallets
• 🧹 <b>Cleanup:</b> Remove inactive wallets

<b>🔥 Enhanced Features:</b>
• ✅ Unlimited wallet tracking
• ✅ Smart validation & duplicate detection
• ✅ Real-time progress tracking
• ✅ Automatic cleanup after 5 days
• ✅ Advanced error handling

<b>Choose an operation from the menu above!</b>
      `
      bot.sendMessage(chatId, manageMessage, { parse_mode: 'HTML' })
      break

    case 'my_wallet':
      const myWalletMessage = `
👛 <b>My Personal Wallet - Enhanced Edition</b>

🚀 <b>Manage your personal wallet!</b>

<b>📊 Current Status:</b>
• Personal wallet: Not set (Demo Mode)
• Balance: N/A
• Status: Ready to configure

<b>🔥 Features:</b>
• ✅ Secure wallet management
• ✅ Balance tracking
• ✅ Transaction history
• ✅ Private key protection

<b>💡 To set up your personal wallet:</b>
Send your wallet address or use the setup command.

<i>Your personal wallet is separate from tracked wallets!</i>
      `
      bot.sendMessage(chatId, myWalletMessage, { parse_mode: 'HTML' })
      break

    case 'settings':
      const settingsMessage = `
⚙️ <b>Bot Settings - Enhanced Edition</b>

🚀 <b>Configure your bot experience!</b>

<b>📊 Current Settings:</b>
• Notifications: Enabled
• Auto cleanup: 5 days
• Tracking mode: Unlimited
• API keys: 2 Helius keys active

<b>🔥 Available Settings:</b>
• 🔔 Notification preferences
• 🧹 Cleanup schedule
• 📊 Tracking limits
• 🔑 API key management
• 🎨 Interface themes

<b>💡 Enhanced Features:</b>
• ✅ Real-time configuration
• ✅ Smart defaults
• ✅ Advanced customization

<i>All settings optimized for unlimited tracking!</i>
      `
      bot.sendMessage(chatId, settingsMessage, { parse_mode: 'HTML' })
      break

    case 'groups':
      const groupsMessage = `
🆕 <b>Group Management - Enhanced Edition</b>

🚀 <b>Manage wallet groups and team tracking!</b>

<b>📊 Group Features:</b>
• 👥 Team wallet tracking
• 📊 Shared monitoring
• 🔔 Group notifications
• 📈 Collective analytics

<b>🔥 Enhanced Capabilities:</b>
• ✅ Unlimited group members
• ✅ Bulk group operations
• ✅ Advanced permissions
• ✅ Real-time collaboration

<b>💡 Getting Started:</b>
1. Create a new group
2. Invite team members
3. Share wallet tracking
4. Monitor together

<i>Perfect for teams and organizations!</i>
      `
      bot.sendMessage(chatId, groupsMessage, { parse_mode: 'HTML' })
      break

    case 'errors':
      const errorsMessage = `
🚨 <b>Error Monitor - Enhanced Edition</b>

🚀 <b>Real-time error tracking and monitoring!</b>

<b>📊 Current Status:</b>
• Error rate: Low (Demo Mode)
• Last error: None
• System health: Excellent
• Uptime: 100%

<b>🔥 Monitoring Features:</b>
• ✅ Real-time error detection
• ✅ Automatic error recovery
• ✅ Smart retry mechanisms
• ✅ Performance optimization

<b>💡 Error Categories:</b>
• 🔗 RPC connection errors
• 🔑 API key issues
• 📊 Rate limiting
• 🛡️ Security alerts

<i>Advanced monitoring keeps your bot running smoothly!</i>
      `
      bot.sendMessage(chatId, errorsMessage, { parse_mode: 'HTML' })
      break

    case 'donate':
      const donateMessage = `
❤️ <b>Support Enhanced Handi Cat - Donation</b>

🚀 <b>Help keep the enhanced features running!</b>

<b>💰 Donation Options:</b>
• 🥉 Bronze: 0.1 SOL - Basic support
• 🥈 Silver: 0.5 SOL - Enhanced support
• 🥇 Gold: 1.0 SOL - Premium support
• 💎 Diamond: 5.0 SOL - Ultimate support

<b>🔥 Donor Benefits:</b>
• ✅ Priority support
• ✅ Early access to new features
• ✅ Special donor badge
• ✅ Enhanced rate limits

<b>💡 Donation Address:</b>
<code>Coming soon in full version</code>

<i>Your support helps maintain unlimited tracking!</i>
      `
      bot.sendMessage(chatId, donateMessage, { parse_mode: 'HTML' })
      break

    case 'upgrade':
      const upgradeMessage = `
👑 <b>Upgrade to Premium - Enhanced Edition</b>

🚀 <b>Unlock even more powerful features!</b>

<b>🔥 Premium Features:</b>
• 🚀 Priority API access
• 📊 Advanced analytics
• 🔔 Custom notifications
• 📈 Historical data
• 🛡️ Enhanced security

<b>💰 Pricing Plans:</b>
• 🥈 Pro: 2 SOL/month - 1000 wallets
• 🥇 Whale: 5 SOL/month - Unlimited + Premium
• 💎 Enterprise: Custom pricing

<b>💡 Current Status:</b>
You're using the Enhanced Free Edition with unlimited tracking!

<i>Already enjoying unlimited features in this enhanced version!</i>
      `
      bot.sendMessage(chatId, upgradeMessage, { parse_mode: 'HTML' })
      break

    case 'help':
      const helpMessage = `
🔎 <b>Enhanced Handi Cat Wallet Tracker - Help</b>

🚀 <b>Enhanced Commands:</b>
/start - Show main menu with all features
/bulk_add - Add multiple wallets at once
/bulk_remove - Remove multiple wallets
/list - View all your tracked wallets
/health - System health status (admin)
/help - Show this help

🔥 <b>New Features:</b>
📦 <b>Bulk Operations:</b> Add/remove up to 100 wallets
⚡ <b>Multiple API Keys:</b> Smart load balancing
🛡️ <b>Auto Cleanup:</b> 5-day inactivity removal
📊 <b>Monitoring:</b> Real-time health tracking

💡 <b>Getting Started:</b>
1. Click "📦 Bulk Add Wallets" to add multiple wallets
2. Use "📊 List My Wallets" to view your tracked wallets
3. Click "👀 Manage Wallets" for full management

<i>Unlimited wallet tracking with enhanced features! 🎉</i>
      `
      bot.sendMessage(chatId, helpMessage, { parse_mode: 'HTML' })
      break

    default:
      bot.sendMessage(chatId, `🔧 Feature "${data}" is coming soon in the enhanced edition!`)
  }
})

// Message handler for wallet addresses
bot.on('message', (msg) => {
  // Skip if it's a command or callback query
  if (msg.text && msg.text.startsWith('/')) return
  if (!msg.text) return

  const chatId = msg.chat.id
  const userName = msg.from.first_name || 'User'
  const text = msg.text.trim()

  console.log(`📱 Received message from ${userName}: ${text}`)

  // Check if it's a Solana wallet address (base58, 32-44 characters)
  const walletRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/

  if (walletRegex.test(text)) {
    // It's a wallet address!
    console.log(`✅ Detected wallet address: ${text}`)

    const successMessage = `
✅ <b>Wallet Added Successfully - Enhanced Edition</b>

🎉 <b>Wallet has been added to tracking!</b>

<b>📊 Wallet Details:</b>
• Address: <code>${text}</code>
• Name: Auto-generated (Wallet ${Math.floor(Math.random() * 1000)})
• Status: Active tracking
• Added: ${new Date().toLocaleString()}

<b>🔥 Enhanced Features Active:</b>
• ✅ Real-time transaction monitoring
• ✅ Smart notification system
• ✅ Automatic cleanup after 5 days
• ✅ Multiple API key failover
• ✅ Advanced error handling

<b>💡 What happens next:</b>
• You'll receive notifications for all transactions
• Wallet will be monitored 24/7
• Auto-cleanup after 5 days of inactivity
• Use /list to see all your tracked wallets

<i>🚀 Unlimited wallet tracking is now active!</i>
    `

    bot.sendMessage(chatId, successMessage, { parse_mode: 'HTML' })
    return
  }

  // Check if it's a wallet address with name (space or comma separated)
  const walletWithNameRegex = /^([1-9A-HJ-NP-Za-km-z]{32,44})[\s,]+(.+)$/
  const match = text.match(walletWithNameRegex)

  if (match) {
    const walletAddress = match[1]
    const walletName = match[2].trim()

    console.log(`✅ Detected wallet with name: ${walletAddress} - ${walletName}`)

    const successWithNameMessage = `
✅ <b>Named Wallet Added Successfully - Enhanced Edition</b>

🎉 <b>Wallet has been added to tracking!</b>

<b>📊 Wallet Details:</b>
• Address: <code>${walletAddress}</code>
• Name: <b>${walletName}</b>
• Status: Active tracking
• Added: ${new Date().toLocaleString()}

<b>🔥 Enhanced Features Active:</b>
• ✅ Real-time transaction monitoring
• ✅ Smart notification system
• ✅ Automatic cleanup after 5 days
• ✅ Multiple API key failover
• ✅ Advanced error handling

<b>💡 What happens next:</b>
• You'll receive notifications for all transactions
• Wallet will be monitored 24/7
• Auto-cleanup after 5 days of inactivity
• Use /list to see all your tracked wallets

<i>🚀 Unlimited wallet tracking is now active!</i>
    `

    bot.sendMessage(chatId, successWithNameMessage, { parse_mode: 'HTML' })
    return
  }

  // If it's not a wallet address, provide helpful guidance
  const helpMessage = `
🤔 <b>Not a valid wallet address - Enhanced Edition</b>

<b>📝 Supported formats:</b>
• <code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1</code> (address only)
• <code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 My Whale Wallet</code> (with name)
• <code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1,DeFi Trader</code> (comma separated)

<b>💡 Tips:</b>
• Solana addresses are 32-44 characters long
• Use base58 encoding (no 0, O, I, l)
• You can add a name after the address (optional)

<b>🔥 Or use the enhanced menu:</b>
• 📦 <b>Bulk Add:</b> Add multiple wallets at once
• ➕ <b>Single Add:</b> Get detailed instructions

<i>Send /start to see the enhanced menu!</i>
  `

  bot.sendMessage(chatId, helpMessage, { parse_mode: 'HTML' })
})

// Error handling
bot.on('error', (error) => {
  console.error('❌ Bot error:', error)
})

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error)
})

console.log('🎉 Enhanced Handi Cat Wallet Tracker is ready!')
console.log('📱 Send /start to test the enhanced interface!')
console.log('🔥 All enhanced features are available in the menu!')

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down enhanced bot...')
  bot.stopPolling()
  process.exit(0)
})
