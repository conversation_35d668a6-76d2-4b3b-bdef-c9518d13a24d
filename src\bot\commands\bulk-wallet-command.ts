import TelegramBot from 'node-telegram-bot-api'
import { BulkWalletService } from '../../services/bulk-wallet-service'
import { logger } from '../../services/logger-service'
import { BACK_BUTTON } from '../../config/bot-menus'
import chalk from 'chalk'

export class BulkWalletCommand {
  private bot: TelegramBot
  private bulkWalletService: BulkWalletService
  private activeOperations: Map<string, boolean> = new Map()

  constructor(bot: TelegramBot) {
    this.bot = bot
    this.bulkWalletService = new BulkWalletService()
  }

  public bulkWalletCommandHandler(): void {
    // Bulk add wallets command
    this.bot.onText(/\/bulk_add/, async (message) => {
      const userId = message.from?.id.toString()
      if (!userId) return

      // Check if user has an active operation
      if (this.activeOperations.get(userId)) {
        this.bot.sendMessage(message.chat.id, '⏳ You already have a bulk operation in progress. Please wait for it to complete.')
        return
      }

      const helpMessage = `
📝 <b>Bulk Add Wallets</b>

Send me a list of wallet addresses to add, one per line. You can include names too!

<b>Supported formats:</b>
• <code>wallet_address</code>
• <code>wallet_address wallet_name</code>
• <code>wallet_address,wallet_name</code>

<b>Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector</code>

💡 <b>Tips:</b>
• You can add up to 100 wallets at once
• Invalid addresses will be skipped
• Duplicate wallets will be skipped
• Names are optional

<b>Send your wallet list now:</b>
      `

      await this.bot.sendMessage(message.chat.id, helpMessage, {
        parse_mode: 'HTML',
        reply_markup: BACK_BUTTON
      })

      // Set up listener for the wallet list
      this.setupBulkAddListener(userId, message.chat.id)
    })

    // Bulk remove wallets command
    this.bot.onText(/\/bulk_remove/, async (message) => {
      const userId = message.from?.id.toString()
      if (!userId) return

      // Check if user has an active operation
      if (this.activeOperations.get(userId)) {
        this.bot.sendMessage(message.chat.id, '⏳ You already have a bulk operation in progress. Please wait for it to complete.')
        return
      }

      const helpMessage = `
🗑️ <b>Bulk Remove Wallets</b>

Send me a list of wallet addresses to remove, one per line.

<b>Format:</b>
<code>wallet_address_1
wallet_address_2
wallet_address_3</code>

<b>Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S</code>

💡 <b>Tips:</b>
• You can remove up to 100 wallets at once
• Only wallets you're currently tracking will be removed
• Invalid/non-tracked addresses will be skipped

<b>Send your wallet list now:</b>
      `

      await this.bot.sendMessage(message.chat.id, helpMessage, {
        parse_mode: 'HTML',
        reply_markup: BACK_BUTTON
      })

      // Set up listener for the wallet list
      this.setupBulkRemoveListener(userId, message.chat.id)
    })

    // List all wallets command
    this.bot.onText(/\/list_wallets/, async (message) => {
      const userId = message.from?.id.toString()
      if (!userId) return

      try {
        const walletCount = await this.bulkWalletService.getUserWalletCount(userId)

        if (walletCount === 0) {
          this.bot.sendMessage(message.chat.id, '📭 You are not tracking any wallets yet.\n\nUse /add to add a single wallet or /bulk_add to add multiple wallets.', {
            reply_markup: BACK_BUTTON,
            parse_mode: 'HTML'
          })
          return
        }

        // For now, just show count. In a full implementation, you'd show the actual list
        const message_text = `
📊 <b>Your Tracked Wallets</b>

👛 <b>Total wallets:</b> ${walletCount}

💡 <b>Available commands:</b>
• /bulk_add - Add multiple wallets
• /bulk_remove - Remove multiple wallets
• /add - Add single wallet
• /delete - Remove single wallet

🚀 <b>Unlimited tracking enabled!</b>
        `

        await this.bot.sendMessage(message.chat.id, message_text, {
          parse_mode: 'HTML',
          reply_markup: BACK_BUTTON
        })

      } catch (error) {
        logger.error('Error getting wallet list', error as Error, {
          component: 'bulk-wallet-command',
          metadata: { userId }
        })
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving your wallet list.')
      }
    })

    console.log(chalk.greenBright('📦 Bulk wallet commands registered'))
  }

  // Button handlers for inline keyboard
  public async handleBulkAddButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    if (!userId) return

    const helpMessage = `
📦 <b>Bulk Add Wallets - Enhanced Edition</b>

🚀 <b>Add up to 100 wallets at once!</b>

<b>📝 Supported formats:</b>
• <code>wallet_address</code>
• <code>wallet_address wallet_name</code>
• <code>wallet_address,wallet_name</code>

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector</code>

💡 <b>Features:</b>
• ✅ Real-time progress tracking
• ✅ Smart validation & duplicate detection
• ✅ Detailed success/failure reporting
• ✅ Unlimited wallet tracking

<b>Send your wallet list now or use /bulk_add command:</b>
    `

    await this.bot.sendMessage(message.chat.id, helpMessage, {
      parse_mode: 'HTML',
      reply_markup: BACK_BUTTON
    })
  }

  public async handleBulkRemoveButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    if (!userId) return

    const helpMessage = `
🗑️ <b>Bulk Remove Wallets - Enhanced Edition</b>

🚀 <b>Remove multiple wallets quickly!</b>

<b>📝 Format:</b>
Send wallet addresses, one per line:

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S</code>

💡 <b>Features:</b>
• ✅ Remove up to 100 wallets at once
• ✅ Progress tracking with live updates
• ✅ Only removes wallets you're tracking
• ✅ Detailed removal summary

<b>Send your wallet list now or use /bulk_remove command:</b>
    `

    await this.bot.sendMessage(message.chat.id, helpMessage, {
      parse_mode: 'HTML',
      reply_markup: BACK_BUTTON
    })
  }

  public async handleListWalletsButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    if (!userId) return

    try {
      const walletCount = await this.bulkWalletService.getUserWalletCount(userId)

      if (walletCount === 0) {
        const message_text = `
📭 <b>No Wallets Tracked</b>

You are not tracking any wallets yet.

🚀 <b>Get Started:</b>
• 📦 <b>Bulk Add:</b> Use /bulk_add to add multiple wallets
• ➕ <b>Single Add:</b> Use /add to add one wallet
• 🔥 <b>Unlimited tracking</b> with enhanced features!

💡 <b>Enhanced Features:</b>
✅ Track unlimited wallets
✅ Bulk operations (up to 100 at once)
✅ Real-time notifications
✅ Automatic cleanup after 5 days
✅ Advanced monitoring
        `
        await this.bot.sendMessage(message.chat.id, message_text, {
          parse_mode: 'HTML',
          reply_markup: BACK_BUTTON
        })
        return
      }

      const message_text = `
📊 <b>Your Tracked Wallets - Enhanced Edition</b>

👛 <b>Total wallets:</b> ${walletCount}
🚀 <b>Status:</b> Unlimited tracking enabled!

💡 <b>Available operations:</b>
• 📦 <b>/bulk_add</b> - Add multiple wallets (up to 100)
• 🗑️ <b>/bulk_remove</b> - Remove multiple wallets
• ➕ <b>/add</b> - Add single wallet
• 🗑️ <b>/delete</b> - Remove single wallet
• 👀 <b>/manage</b> - Manage all wallets

🔥 <b>Enhanced Features Active:</b>
✅ Multiple Helius API keys for reliability
✅ Smart load balancing & failover
✅ Automatic cleanup after 5 days inactivity
✅ Real-time transaction notifications
✅ Comprehensive error tracking & monitoring

<i>🎉 Unlimited wallet tracking with bulk operations!</i>
      `

      await this.bot.sendMessage(message.chat.id, message_text, {
        parse_mode: 'HTML',
        reply_markup: BACK_BUTTON
      })

    } catch (error) {
      console.error('Error getting wallet list:', error)
      this.bot.sendMessage(message.chat.id, '❌ Error retrieving your wallet list.')
    }
  }

  private setupBulkAddListener(userId: string, chatId: number): void {
    const timeoutId = setTimeout(() => {
      this.activeOperations.delete(userId)
      this.bot.sendMessage(chatId, '⏰ Bulk add operation timed out. Please try again with /bulk_add')
    }, 5 * 60 * 1000) // 5 minutes timeout

    const messageListener = async (message: TelegramBot.Message) => {
      if (message.from?.id.toString() !== userId || message.chat.id !== chatId) return
      if (!message.text || message.text.startsWith('/')) {
        return // Ignore commands
      }

      // Clear timeout and remove listener
      clearTimeout(timeoutId)
      this.bot.removeListener('message', messageListener)
      this.activeOperations.delete(userId)

      await this.processBulkAdd(userId, chatId, message.text)
    }

    this.bot.on('message', messageListener)
  }

  private setupBulkRemoveListener(userId: string, chatId: number): void {
    const timeoutId = setTimeout(() => {
      this.activeOperations.delete(userId)
      this.bot.sendMessage(chatId, '⏰ Bulk remove operation timed out. Please try again with /bulk_remove')
    }, 5 * 60 * 1000) // 5 minutes timeout

    const messageListener = async (message: TelegramBot.Message) => {
      if (message.from?.id.toString() !== userId || message.chat.id !== chatId) return
      if (!message.text || message.text.startsWith('/')) {
        return // Ignore commands
      }

      // Clear timeout and remove listener
      clearTimeout(timeoutId)
      this.bot.removeListener('message', messageListener)
      this.activeOperations.delete(userId)

      await this.processBulkRemove(userId, chatId, message.text)
    }

    this.bot.on('message', messageListener)
  }

  private async processBulkAdd(userId: string, chatId: number, text: string): Promise<void> {
    try {
      // Mark operation as active
      this.activeOperations.set(userId, true)

      // Parse wallet input
      const wallets = this.bulkWalletService.parseWalletInput(text)

      if (wallets.length === 0) {
        this.bot.sendMessage(chatId, '❌ No valid wallet addresses found. Please check your format and try again.')
        this.activeOperations.delete(userId)
        return
      }

      if (wallets.length > 100) {
        this.bot.sendMessage(chatId, '❌ Too many wallets! Please limit to 100 wallets per batch.')
        this.activeOperations.delete(userId)
        return
      }

      // Send initial progress message
      const progressMessage = await this.bot.sendMessage(
        chatId,
        `🔄 <b>Processing ${wallets.length} wallets...</b>\n\n⏳ Starting bulk add operation...`,
        { parse_mode: 'HTML' }
      )

      // Process wallets with progress updates
      let lastProgressUpdate = 0
      const summary = await this.bulkWalletService.addWalletsBulk(
        userId,
        wallets,
        async (progress, currentWallet) => {
          // Update progress every 10% or every 10 wallets
          if (progress - lastProgressUpdate >= 10 || progress === 100) {
            lastProgressUpdate = progress
            const shortAddress = `${currentWallet.substring(0, 8)}...${currentWallet.substring(currentWallet.length - 4)}`
            await this.bot.editMessageText(
              `🔄 <b>Processing ${wallets.length} wallets...</b>\n\n📊 Progress: ${progress}%\n🔍 Current: ${shortAddress}`,
              {
                chat_id: chatId,
                message_id: progressMessage.message_id,
                parse_mode: 'HTML'
              }
            ).catch(() => {}) // Ignore edit errors
          }
        }
      )

      // Send final summary
      const summaryMessage = this.bulkWalletService.generateSummaryMessage(summary, 'add')
      await this.bot.editMessageText(summaryMessage, {
        chat_id: chatId,
        message_id: progressMessage.message_id,
        parse_mode: 'HTML',
        reply_markup: BACK_BUTTON
      })

      logger.info('Bulk add operation completed', {
        component: 'bulk-wallet-command',
        metadata: {
          userId,
          totalProcessed: summary.totalProcessed,
          successful: summary.successful,
          failed: summary.failed,
          skipped: summary.skipped
        }
      })

    } catch (error) {
      logger.error('Error in bulk add operation', error as Error, {
        component: 'bulk-wallet-command',
        metadata: { userId }
      })
      this.bot.sendMessage(chatId, '❌ An error occurred during the bulk add operation. Please try again.')
    } finally {
      this.activeOperations.delete(userId)
    }
  }

  private async processBulkRemove(userId: string, chatId: number, text: string): Promise<void> {
    try {
      // Mark operation as active
      this.activeOperations.set(userId, true)

      // Parse wallet addresses
      const addresses = text.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)

      if (addresses.length === 0) {
        this.bot.sendMessage(chatId, '❌ No wallet addresses found. Please check your format and try again.')
        this.activeOperations.delete(userId)
        return
      }

      if (addresses.length > 100) {
        this.bot.sendMessage(chatId, '❌ Too many wallets! Please limit to 100 wallets per batch.')
        this.activeOperations.delete(userId)
        return
      }

      // Send initial progress message
      const progressMessage = await this.bot.sendMessage(
        chatId,
        `🔄 <b>Removing ${addresses.length} wallets...</b>\n\n⏳ Starting bulk remove operation...`,
        { parse_mode: 'HTML' }
      )

      // Process wallets with progress updates
      let lastProgressUpdate = 0
      const summary = await this.bulkWalletService.removeWalletsBulk(
        userId,
        addresses,
        async (progress, currentWallet) => {
          // Update progress every 10% or every 10 wallets
          if (progress - lastProgressUpdate >= 10 || progress === 100) {
            lastProgressUpdate = progress
            const shortAddress = `${currentWallet.substring(0, 8)}...${currentWallet.substring(currentWallet.length - 4)}`
            await this.bot.editMessageText(
              `🔄 <b>Removing ${addresses.length} wallets...</b>\n\n📊 Progress: ${progress}%\n🔍 Current: ${shortAddress}`,
              {
                chat_id: chatId,
                message_id: progressMessage.message_id,
                parse_mode: 'HTML'
              }
            ).catch(() => {}) // Ignore edit errors
          }
        }
      )

      // Send final summary
      const summaryMessage = this.bulkWalletService.generateSummaryMessage(summary, 'remove')
      await this.bot.editMessageText(summaryMessage, {
        chat_id: chatId,
        message_id: progressMessage.message_id,
        parse_mode: 'HTML',
        reply_markup: BACK_BUTTON
      })

      logger.info('Bulk remove operation completed', {
        component: 'bulk-wallet-command',
        metadata: {
          userId,
          totalProcessed: summary.totalProcessed,
          successful: summary.successful,
          failed: summary.failed,
          skipped: summary.skipped
        }
      })

    } catch (error) {
      logger.error('Error in bulk remove operation', error as Error, {
        component: 'bulk-wallet-command',
        metadata: { userId }
      })
      this.bot.sendMessage(chatId, '❌ An error occurred during the bulk remove operation. Please try again.')
    } finally {
      this.activeOperations.delete(userId)
    }
  }
}
