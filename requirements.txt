# Handi Cat Wallet Tracker Enhanced Edition - System Requirements
# ================================================================

# Node.js Runtime Requirements
# ============================
# Node.js version: 14.x or higher (recommended: 18.x or 20.x)
# Package Manager: pnpm (recommended) or npm

# Core Dependencies (automatically installed via package.json)
# ============================================================

# Blockchain & Solana
# -------------------
# @solana/web3.js ^1.94.0          # Solana blockchain interaction
# @solana/spl-token ^0.3.0         # SPL token handling
# @solana/spl-token-registry ^0.2.4574  # Token registry
# @metaplex-foundation/mpl-token-metadata 2.0.0  # NFT metadata
# @raydium-io/raydium-sdk 1.3.1-beta.58  # Raydium DEX integration

# Telegram Bot
# ------------
# node-telegram-bot-api ^0.66.0    # Telegram bot framework

# Database & ORM
# --------------
# @prisma/client ^5.16.2          # Database ORM client
# prisma ^5.16.2                   # Database toolkit

# HTTP & API
# ----------
# axios ^1.7.7                     # HTTP client for API calls
# express ^4.19.2                  # Web server framework

# Utilities
# ---------
# dotenv ^16.4.5                   # Environment variable management
# bs58 ^6.0.0                      # Base58 encoding/decoding
# date-fns ^3.6.0                  # Date manipulation
# p-limit 3.0.0                    # Concurrency control

# Logging & Monitoring
# -------------------
# winston ^3.11.0                  # Advanced logging
# winston-daily-rotate-file ^4.7.1 # Log file rotation

# Scheduling
# ----------
# node-cron ^3.0.3                 # Cron job scheduling

# UI & Display
# ------------
# chalk 4.1.2                      # Terminal colors
# gradient-string ^2.0.0           # Gradient text effects

# Development Dependencies
# =======================
# typescript ^5.5.2                # TypeScript compiler
# ts-node ^10.9.2                  # TypeScript execution
# @types/node ^20.14.9             # Node.js type definitions
# @types/express ^4.17.21          # Express type definitions
# @types/node-telegram-bot-api ^0.64.7  # Telegram bot types
# @types/node-cron ^3.0.11         # Cron types
# @types/gradient-string ^1.1.6    # Gradient string types
# @types/winston ^2.4.4            # Winston types
# prettier ^3.3.3                  # Code formatting

# External Service Requirements
# =============================

# Required API Keys & Services
# ----------------------------
# 1. Multiple Helius API Keys (3-5 recommended)
#    - Get from: https://helius.xyz
#    - Free tier: 100k requests/day per key
#    - Format: HELIUS_API_KEYS=key1,key2,key3,key4,key5

# 2. Telegram Bot Token
#    - Get from: @BotFather on Telegram
#    - Format: BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# 3. Database (choose one)
#    - PostgreSQL (recommended): Supabase (500MB free)
#    - MySQL: PlanetScale (5GB free)
#    - MongoDB: MongoDB Atlas (512MB free)
#    - SQLite: Local development only

# Optional RPC Endpoints (for redundancy)
# --------------------------------------
# 1. QuickNode: https://quicknode.com (100k requests/day free)
# 2. Alchemy: https://alchemy.com (300M compute units/month free)
# 3. Chainstack: https://chainstack.com (100k requests/day free)
# 4. Ankr: https://ankr.com (unlimited with rate limits)

# System Requirements
# ==================
# RAM: 512MB minimum, 1GB recommended
# Storage: 1GB minimum (for logs and database)
# Network: Stable internet connection
# OS: Windows, macOS, Linux

# Installation Commands
# ====================
# 1. Install Node.js 14.x or higher
# 2. Install pnpm: npm install -g pnpm
# 3. Clone repository: git clone <repo-url>
# 4. Install dependencies: pnpm install
# 5. Configure environment: cp .env.example .env
# 6. Setup database: pnpm db:setup
# 7. Start bot: pnpm start

# Environment Variables Required
# =============================
# HELIUS_API_KEYS=key1,key2,key3,key4,key5
# BOT_TOKEN=your_telegram_bot_token
# ADMIN_CHAT_ID=your_telegram_chat_id
# DATABASE_URL=your_database_connection_string
# LOG_LEVEL=info
# ENABLE_FILE_LOGGING=true
# ENABLE_ERROR_TRACKING=true

# Performance Recommendations
# ===========================
# - Use 3-5 Helius API keys for optimal performance
# - Enable file logging for debugging
# - Use PostgreSQL for best performance
# - Monitor system health with /health command
# - Set up log rotation to prevent disk space issues

# Scaling Guidelines
# ==================
# Starter (100-300 wallets): 3 API keys, 512MB RAM
# Professional (500-1000 wallets): 5 API keys, 1GB RAM
# Enterprise (1000+ wallets): 10+ API keys, 2GB+ RAM

# Security Considerations
# ======================
# - Keep API keys secure and never commit to version control
# - Use environment variables for all sensitive data
# - Regularly rotate API keys
# - Monitor for unusual activity with admin commands
# - Enable comprehensive logging for audit trails

# Support & Documentation
# =======================
# - README.md: Complete setup and usage guide
# - BULK-WALLET-GUIDE.md: Bulk operations guide
# - LOGGING-GUIDE.md: Comprehensive logging guide
# - WEBHOOK-VS-POLLING-GUIDE.md: Connection mode guide
# - QUICK-START-GUIDE.md: 5-minute setup guide
