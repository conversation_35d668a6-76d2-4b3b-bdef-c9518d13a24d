import logging
import time
import asyncio
from datetime import datetime
from typing import Dict, Any, Set, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from helius import get_token_info

# Set up logging
logger = logging.getLogger(__name__)

class PriceTracker:
    """
    Tracks token prices and sends notifications when they reach certain thresholds.
    """
    def __init__(self):
        # Structure: {token_address: {chat_id: {timestamp, initial_price, current_price, name, symbol, notified_thresholds, notified_negative_35, reached_100_first}}}
        self.tracked_tokens: Dict[str, Dict[int, Dict[str, Any]]] = {}
        self.scheduler = BackgroundScheduler()

        # Define specific notification thresholds as requested
        self.notification_thresholds = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 2000, 5000, 7000, 10000]

        # Define negative threshold for price drop alert
        self.negative_threshold = -35

        # Define trailing stop loss parameters
        self.trailing_stop_loss_enabled = True
        self.trailing_stop_loss_percentage = -10  # Initial stop loss percentage (negative value)
        self.trailing_stop_loss_notified = False  # Flag to track if trailing stop loss notification has been sent

    async def add_token(self, token_address: str, chat_id: int) -> Dict[str, Any]:
        """
        Add a token to be tracked for a specific chat.
        """
        # Fetch token info from GeckoTerminal
        token_info = get_token_info(token_address)

        if not token_info["success"]:
            return token_info

        token_data = token_info["data"]
        price = token_data["price"]
        name = token_data["name"]
        symbol = token_data["symbol"]

        # Initialize token tracking data
        if token_address not in self.tracked_tokens:
            self.tracked_tokens[token_address] = {}

        # Set up tracking for this user
        token_data = {
            "timestamp": int(time.time()),
            "initial_price": price,
            "current_price": price,
            "max_price": price,  # Track the maximum price reached for trailing stop loss
            "name": name,
            "symbol": symbol,
            "notified_negative_35": False,  # Flag for -35% notification
            "reached_50_first": False,     # Flag to track if 50% was reached first
            "notified_trailing_stop_loss": False,  # Flag for trailing stop loss notification
            "trailing_stop_loss_threshold": None,  # Current trailing stop loss threshold
            "notified_thresholds": {}  # To store which thresholds have been notified
        }

        # Initialize all notification thresholds as not notified
        for threshold in self.notification_thresholds:
            token_data["notified_thresholds"][str(threshold)] = False

        self.tracked_tokens[token_address][chat_id] = token_data

        logger.info(f"Added token {token_address} for chat {chat_id} with initial price ${price}")

        return {
            "success": True,
            "data": {
                "name": name,
                "symbol": symbol,
                "initial_price": price,
                "address": token_address
            }
        }

    def remove_token(self, token_address: str, chat_id: int) -> Dict[str, Any]:
        """
        Remove a token from tracking for a specific chat.
        """
        if token_address not in self.tracked_tokens:
            return {
                "success": False,
                "error": "Token is not being tracked"
            }

        if chat_id not in self.tracked_tokens[token_address]:
            return {
                "success": False,
                "error": "You are not tracking this token"
            }

        # Remove token tracking for this user
        del self.tracked_tokens[token_address][chat_id]

        # If no more users are tracking this token, remove it entirely
        if not self.tracked_tokens[token_address]:
            del self.tracked_tokens[token_address]

        logger.info(f"Removed token {token_address} for chat {chat_id}")

        return {
            "success": True
        }

    def get_user_tokens(self, chat_id: int) -> Dict[str, Dict[str, Any]]:
        """
        Get all tokens being tracked by a specific user.
        """
        user_tokens = {}

        for token_address, chats in self.tracked_tokens.items():
            if chat_id in chats:
                user_tokens[token_address] = chats[chat_id]

        return user_tokens

    def clear_all_tokens(self, chat_id: int) -> Dict[str, Any]:
        """
        Remove all tokens being tracked by a specific user.
        Returns success status and count of tokens removed.
        """
        tokens_to_remove = []
        removed_count = 0

        # Find all tokens tracked by this user
        for token_address, chats in list(self.tracked_tokens.items()):
            if chat_id in chats:
                tokens_to_remove.append(token_address)

        # Remove them one by one
        for token_address in tokens_to_remove:
            # Remove token tracking for this user
            del self.tracked_tokens[token_address][chat_id]
            removed_count += 1

            # If no more users are tracking this token, remove it entirely
            if not self.tracked_tokens[token_address]:
                del self.tracked_tokens[token_address]

        logger.info(f"Cleared all tokens ({removed_count} total) for chat {chat_id}")

        return {
            "success": True,
            "count": removed_count
        }

    async def check_token_prices(self, bot) -> None:
        """
        Check prices for all tracked tokens and send notifications if thresholds are reached.
        Also, remove tokens that have been tracked for more than 24 hours.
        """
        if not self.tracked_tokens:
            return

        logger.info(f"Checking prices for {len(self.tracked_tokens)} tokens")
        current_time = int(time.time())
        tokens_to_notify_expiry = []  # To store tokens that will be auto-removed

        for token_address, chats in list(self.tracked_tokens.items()):
            # Get current token price
            token_info = get_token_info(token_address)

            if not token_info["success"]:
                logger.error(f"Failed to get price for {token_address}: {token_info['error']}")
                continue

            current_price = token_info["data"]["price"]
            token_name = token_info["data"]["name"]
            token_symbol = token_info["data"]["symbol"]

            logger.debug(f"Token {token_address} ({token_name}) current price: ${current_price}")

            # Update current price for all chats tracking this token
            for chat_id, data in list(chats.items()):
                initial_price = data["initial_price"]
                previous_price = data["current_price"]
                tracked_since = data["timestamp"]

                # Update the current price
                self.tracked_tokens[token_address][chat_id]["current_price"] = current_price

                # Update max price if current price is higher (for trailing stop loss)
                if current_price > data.get("max_price", 0):
                    self.tracked_tokens[token_address][chat_id]["max_price"] = current_price

                    # Calculate new trailing stop loss threshold using the formula:
                    # New Sell-lo% = (100% + Starting Sell-lo %) * (100% + Max P/L%) - 100%
                    if self.trailing_stop_loss_enabled:
                        max_pl_pct = ((current_price - initial_price) / initial_price) * 100
                        if max_pl_pct > 0:  # Only adjust trailing stop loss if in profit
                            new_threshold = (1 + self.trailing_stop_loss_percentage/100) * (1 + max_pl_pct/100) - 1
                            new_threshold = new_threshold * 100  # Convert back to percentage
                            self.tracked_tokens[token_address][chat_id]["trailing_stop_loss_threshold"] = new_threshold

                # Update name and symbol if they changed or were empty
                if not data["name"] and token_name:
                    self.tracked_tokens[token_address][chat_id]["name"] = token_name
                if not data["symbol"] and token_symbol:
                    self.tracked_tokens[token_address][chat_id]["symbol"] = token_symbol

                # Check if token has been tracked for more than 24 hours (86400 seconds)
                if current_time - tracked_since > 86400:
                    display_name = token_name if token_name else token_address
                    tokens_to_notify_expiry.append({
                        "chat_id": chat_id,
                        "token_address": token_address,
                        "name": display_name
                    })
                    continue

                # Skip if initial price is zero
                if initial_price == 0:
                    continue

                # Calculate percentage change
                pct_change = ((current_price - initial_price) / initial_price) * 100

                # Check notification thresholds for price increases
                for threshold in self.notification_thresholds:
                    threshold_key = str(threshold)
                    # Check if we have reached this threshold and haven't notified yet
                    if pct_change >= threshold and not data["notified_thresholds"].get(threshold_key, True):
                        await self._send_notification(bot, chat_id, token_address, token_name, threshold, current_price, initial_price)
                        self.tracked_tokens[token_address][chat_id]["notified_thresholds"][threshold_key] = True

                        # If we reach 50%, mark the special flag for the -35% condition
                        if threshold == 50:
                            self.tracked_tokens[token_address][chat_id]["reached_50_first"] = True

                # Check for trailing stop loss trigger
                trailing_stop_loss_threshold = data.get("trailing_stop_loss_threshold")
                if (self.trailing_stop_loss_enabled and
                    trailing_stop_loss_threshold is not None and
                    pct_change <= trailing_stop_loss_threshold and
                    not data.get("notified_trailing_stop_loss", False)):

                    # Send trailing stop loss notification
                    await self._send_trailing_stop_loss_notification(
                        bot, chat_id, token_address, token_name,
                        trailing_stop_loss_threshold, current_price, initial_price,
                        data["max_price"]
                    )

                    # Mark as notified
                    self.tracked_tokens[token_address][chat_id]["notified_trailing_stop_loss"] = True

                # Check for price decrease below -35%
                if pct_change <= self.negative_threshold and not data["notified_negative_35"]:
                    # Only send notification if the token hasn't reached 50% first
                    if not data["reached_50_first"]:
                        await self._send_negative_notification(bot, chat_id, token_address, token_name, self.negative_threshold, current_price, initial_price)

                    # Add token to a list to be removed after the loop (regardless of whether notification was sent)
                    tokens_to_notify_expiry.append({
                        "chat_id": chat_id,
                        "token_address": token_address,
                        "name": token_name if token_name else token_address,
                        "reason": "negative_threshold",
                        "reached_50_first": data["reached_50_first"]  # Pass this flag to the removal handler
                    })

                    # Mark as notified regardless, so we don't check again
                    self.tracked_tokens[token_address][chat_id]["notified_negative_35"] = True

        # Remove expired tokens and notify users
        for token_info in tokens_to_notify_expiry:
            chat_id = token_info["chat_id"]
            token_address = token_info["token_address"]
            token_name = token_info["name"]

            # Send expiry notification
            if token_info.get("reason") == "negative_threshold":
                # Only send the negative threshold notification if the token didn't reach 50% first
                if not token_info.get("reached_50_first", False):
                    expiry_message = (
                        f"📉 <b>Auto-removal:</b> {token_name} ({token_address[:6]}...{token_address[-4:]}) has been automatically removed because it dropped below -35%.\n\n"
                        f"To track this token again, use: /track {token_address}"
                    )
                    try:
                        bot.send_message(chat_id, expiry_message)
                        logger.info(f"Sent negative threshold removal notification for {token_address} to chat {chat_id}")
                    except Exception as e:
                        logger.error(f"Failed to send auto-removal notification to chat {chat_id}: {str(e)}")
                else:
                    # Token reached 50% first, so we silently remove it without notification
                    logger.info(f"Silently removed token {token_address} for chat {chat_id} (reached 50% first, then dropped below -35%)")
            else:
                # This is the 24-hour expiry notification
                expiry_message = (
                    f"⏰ <b>Auto-removal:</b> {token_name} ({token_address[:6]}...{token_address[-4:]}) has been automatically removed after 24 hours of tracking.\n\n"
                    f"To track this token again, use: /track {token_address}"
                )
                try:
                    bot.send_message(chat_id, expiry_message)
                    logger.info(f"Sent 24-hour expiry notification for {token_address} to chat {chat_id}")
                except Exception as e:
                    logger.error(f"Failed to send auto-removal notification to chat {chat_id}: {str(e)}")

            # Remove token for this chat
            if token_address in self.tracked_tokens and chat_id in self.tracked_tokens[token_address]:
                del self.tracked_tokens[token_address][chat_id]
                if token_info.get("reason") == "negative_threshold":
                    logger.info(f"Auto-removed token {token_address} for chat {chat_id} due to -35% drop")
                else:
                    logger.info(f"Auto-removed token {token_address} for chat {chat_id} after 24 hours")

                # If no more users are tracking this token, remove it entirely
                if not self.tracked_tokens[token_address]:
                    del self.tracked_tokens[token_address]

    async def _send_notification(self, bot, chat_id: int, token_address: str, token_name: str,
                               threshold: int, current_price: float, initial_price: float) -> None:
        """
        Send a price alert notification to a specific chat for price increases.
        """
        display_name = token_name if token_name else token_address

        # Calculate actual percentage
        actual_pct = ((current_price - initial_price) / initial_price) * 100

        message = (
            f"🚀 Token {display_name} is up {actual_pct:.2f}%!\n\n"
            f"Initial price: ${initial_price:.8f}\n"
            f"Current price: ${current_price:.8f}\n\n"
            f"View on Helius: https://xray.helius.xyz/token/{token_address}"
        )

        try:
            bot.send_message(chat_id, message)
            logger.info(f"Sent {threshold}% alert for {token_address} to chat {chat_id}")
        except Exception as e:
            logger.error(f"Failed to send notification to chat {chat_id}: {str(e)}")

    async def _send_negative_notification(self, bot, chat_id: int, token_address: str, token_name: str,
                                      threshold: int, current_price: float, initial_price: float) -> None:
        """
        Send a price alert notification to a specific chat for price decreases.
        """
        display_name = token_name if token_name else token_address

        # Calculate actual percentage (will be negative)
        actual_pct = ((current_price - initial_price) / initial_price) * 100

        message = (
            f"⚠️ Alert! Token {display_name} has dropped {actual_pct:.2f}%!\n\n"
            f"Initial price: ${initial_price:.8f}\n"
            f"Current price: ${current_price:.8f}\n\n"
            f"View on Helius: https://xray.helius.xyz/token/{token_address}"
        )

        try:
            bot.send_message(chat_id, message)
            logger.info(f"Sent negative {threshold}% alert for {token_address} to chat {chat_id}")
        except Exception as e:
            logger.error(f"Failed to send negative notification to chat {chat_id}: {str(e)}")

    async def _send_trailing_stop_loss_notification(self, bot, chat_id: int, token_address: str, token_name: str,
                                               threshold: float, current_price: float, initial_price: float, max_price: float) -> None:
        """
        Send a trailing stop loss notification to a specific chat.
        """
        display_name = token_name if token_name else token_address

        # Calculate percentages
        current_pct = ((current_price - initial_price) / initial_price) * 100
        max_pct = ((max_price - initial_price) / initial_price) * 100
        drop_from_max = ((current_price - max_price) / max_price) * 100

        message = (
            f"🔻 <b>Trailing Stop Loss Triggered!</b> {display_name}\n\n"
            f"Current P/L: {current_pct:.2f}%\n"
            f"Maximum P/L reached: {max_pct:.2f}%\n"
            f"Drop from maximum: {drop_from_max:.2f}%\n\n"
            f"Initial price: ${initial_price:.8f}\n"
            f"Maximum price: ${max_price:.8f}\n"
            f"Current price: ${current_price:.8f}\n\n"
            f"This is a good time to consider taking profits based on your trailing stop loss strategy.\n\n"
            f"View on Helius: https://xray.helius.xyz/token/{token_address}"
        )

        try:
            bot.send_message(chat_id, message)
            logger.info(f"Sent trailing stop loss alert for {token_address} to chat {chat_id} (threshold: {threshold:.2f}%)")
        except Exception as e:
            logger.error(f"Failed to send trailing stop loss notification to chat {chat_id}: {str(e)}")

    def run_check_token_prices(self, bot):
        """
        Run the check_token_prices function in a new event loop
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.check_token_prices(bot))
        except Exception as e:
            logger.error(f"Error in check_token_prices: {str(e)}")
        finally:
            loop.close()

    def start_price_checking(self, bot) -> None:
        """
        Start the scheduler to periodically check token prices.
        """
        # Generate a unique job ID based on current timestamp
        import time
        unique_job_id = f'price_checker_{int(time.time())}'

        # Check prices every 15 seconds for near real-time notifications
        import pytz
        self.scheduler.add_job(
            lambda: self.run_check_token_prices(bot),
            'interval',
            seconds=15,
            id=unique_job_id,
            timezone=pytz.UTC
        )

        logger.info(f"Starting price checking scheduler with job ID: {unique_job_id}")
        self.scheduler.start()
