import logging
import requests
import re

# Set up logging
logger = logging.getLogger(__name__)

# Constants
BASE_URL = "https://api.geckoterminal.com/api/v2"
SOLANA_NETWORK_ID = "solana"

def is_valid_solana_address(address):
    """
    Validate a Solana address format.
    Solana addresses are base58 encoded and typically 32-44 characters.
    This also accepts pool addresses which may contain additional characters.
    """
    logger.info(f"Validating Solana address: {address}")
    
    # Check if it's a pool address (like 6Z5HhKvcmLGwKRftZpDc6yj8TYDaaP8T8RYhiie2pump)
    if len(address) >= 20:
        # More permissive pattern for token addresses and pool addresses
        pattern = r'^[0-9A-Za-z]{20,44}$'
        is_valid = bool(re.match(pattern, address))
        logger.info(f"Address validation result: {is_valid}")
        return is_valid
    
    logger.info(f"Address validation failed - too short")
    return False

def get_token_info(token_address):
    """
    Get token information from GeckoTerminal API.
    Returns token details and price if successful.
    Can accept both token addresses and pool addresses.
    """
    try:
        logger.info(f"Getting token info for address: {token_address}")
        
        # First, try to treat it as a direct pool address
        pool_url = f"{BASE_URL}/networks/{SOLANA_NETWORK_ID}/pools/{token_address}"
        logger.info(f"Trying direct pool approach with URL: {pool_url}")
        
        try:
            pool_response = requests.get(pool_url)
            pool_response.raise_for_status()
            
            pool_data = pool_response.json()
            
            if 'data' in pool_data and 'attributes' in pool_data['data']:
                # It's a valid pool address
                logger.info(f"Successfully found pool data for {token_address}")
                
                pool_attributes = pool_data['data']['attributes']
                base_token_address = pool_attributes.get('base_address', '')
                base_token_name = pool_attributes.get('base_name', '')
                base_token_symbol = pool_attributes.get('base_symbol', '')
                token_price_usd = float(pool_attributes.get('base_token_price_usd', 0))
                
                return {
                    "success": True,
                    "data": {
                        "name": base_token_name,
                        "symbol": base_token_symbol,
                        "price": token_price_usd,
                        "address": base_token_address or token_address
                    }
                }
        except Exception as e:
            logger.info(f"Not a direct pool address, trying token lookup: {str(e)}")
            # Continue with token lookup
        
        # If we get here, try the token approach
        token_url = f"{BASE_URL}/networks/{SOLANA_NETWORK_ID}/tokens/{token_address}"
        logger.info(f"Requesting token info from: {token_url}")
        
        response = requests.get(token_url)
        response.raise_for_status()
        
        token_data = response.json()
        
        if 'data' not in token_data:
            logger.error(f"Invalid response format for token {token_address}: {token_data}")
            return {
                "success": False,
                "error": "Token not found or invalid response from GeckoTerminal"
            }
        
        # Extract token details
        token_info = token_data['data']['attributes']
        token_name = token_info.get('name', '')
        token_symbol = token_info.get('symbol', '')
        
        # Now get the top pool for this token to get the price
        pools_url = f"{BASE_URL}/networks/{SOLANA_NETWORK_ID}/tokens/{token_address}/pools"
        logger.info(f"Requesting pools info from: {pools_url}")
        
        pools_response = requests.get(pools_url)
        pools_response.raise_for_status()
        
        pools_data = pools_response.json()
        
        if 'data' not in pools_data or not pools_data['data']:
            return {
                "success": False,
                "error": "No liquidity pools found for this token"
            }
        
        # Get the first (usually most liquid) pool
        top_pool = pools_data['data'][0]
        pool_address = top_pool['id'].split('_')[1]
        
        # Now get the pool details to get the price
        pool_url = f"{BASE_URL}/networks/{SOLANA_NETWORK_ID}/pools/{pool_address}"
        logger.info(f"Requesting pool details from: {pool_url}")
        
        pool_response = requests.get(pool_url)
        pool_response.raise_for_status()
        
        pool_data = pool_response.json()
        
        if 'data' not in pool_data or 'attributes' not in pool_data['data']:
            return {
                "success": False,
                "error": "Failed to get pool details"
            }
        
        # Extract price information
        pool_attributes = pool_data['data']['attributes']
        token_price_usd = float(pool_attributes.get('base_token_price_usd', 0))
        
        return {
            "success": True,
            "data": {
                "name": token_name,
                "symbol": token_symbol,
                "price": token_price_usd,
                "address": token_address
            }
        }
    
    except requests.exceptions.RequestException as e:
        logger.error(f"API request error for token {token_address}: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to fetch token data: {str(e)}"
        }
    except (KeyError, ValueError, TypeError) as e:
        logger.error(f"Data parsing error for token {token_address}: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to parse token data: {str(e)}"
        }
