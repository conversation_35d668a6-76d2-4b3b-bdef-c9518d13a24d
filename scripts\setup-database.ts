import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import chalk from 'chalk'
import dotenv from 'dotenv'

dotenv.config()

const DATABASE_PROVIDER = process.env.DATABASE_PROVIDER || 'postgresql'

async function setupDatabase() {
  console.log(chalk.blueBright('🗄️ Setting up database...'))
  console.log(chalk.cyanBright(`Database Provider: ${DATABASE_PROVIDER}`))

  try {
    // Handle different database providers
    switch (DATABASE_PROVIDER.toLowerCase()) {
      case 'mongodb':
        await setupMongoDB()
        break
      case 'mysql':
        await setupMySQL()
        break
      case 'postgresql':
      case 'postgres':
        await setupPostgreSQL()
        break
      case 'sqlite':
        await setupSQLite()
        break
      default:
        throw new Error(`Unsupported database provider: ${DATABASE_PROVIDER}`)
    }

    console.log(chalk.greenBright('✅ Database setup completed successfully!'))
  } catch (error) {
    console.error(chalk.redBright('❌ Database setup failed:'), error)
    process.exit(1)
  }
}

async function setupMongoDB() {
  console.log(chalk.yellowBright('📦 Setting up MongoDB...'))
  
  // Copy MongoDB schema
  const mongoSchemaPath = path.join(__dirname, '..', 'prisma', 'schema.mongodb.prisma')
  const schemaPath = path.join(__dirname, '..', 'prisma', 'schema.prisma')
  
  if (fs.existsSync(mongoSchemaPath)) {
    fs.copyFileSync(mongoSchemaPath, schemaPath)
    console.log(chalk.greenBright('✅ MongoDB schema copied'))
  }

  // Generate Prisma client
  execSync('npx prisma generate', { stdio: 'inherit' })
  
  // MongoDB doesn't need migrations, just push the schema
  execSync('npx prisma db push', { stdio: 'inherit' })
  
  console.log(chalk.greenBright('✅ MongoDB setup complete'))
}

async function setupMySQL() {
  console.log(chalk.yellowBright('🐬 Setting up MySQL...'))
  
  // Generate Prisma client
  execSync('npx prisma generate', { stdio: 'inherit' })
  
  // Run migrations
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' })
  
  console.log(chalk.greenBright('✅ MySQL setup complete'))
}

async function setupPostgreSQL() {
  console.log(chalk.yellowBright('🐘 Setting up PostgreSQL...'))
  
  // Generate Prisma client
  execSync('npx prisma generate', { stdio: 'inherit' })
  
  // Run migrations
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' })
  
  console.log(chalk.greenBright('✅ PostgreSQL setup complete'))
}

async function setupSQLite() {
  console.log(chalk.yellowBright('📁 Setting up SQLite...'))
  
  // Generate Prisma client
  execSync('npx prisma generate', { stdio: 'inherit' })
  
  // Run migrations
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' })
  
  console.log(chalk.greenBright('✅ SQLite setup complete'))
}

// Run setup
setupDatabase()
