import { Connection, PublicKey } from '@solana/web3.js'
import { RpcConnectionManager } from '../providers/solana'
import { ValidTransactions } from '../lib/valid-transactions'
import { TransactionParser } from '../parsers/transaction-parser'
import { CronJobs } from '../lib/cron-jobs'
import { RateLimit } from '../lib/rate-limit'
import chalk from 'chalk'
import EventEmitter from 'events'

export interface WalletTransactionEvent {
  walletAddress: string
  transactionSignature: string
  transactionType: 'buy' | 'sell' | 'transfer'
  tokenAddress?: string
  amount?: number
  priceUsd?: number
  marketCap?: number
  description: string
  timestamp: number
}

export interface StandaloneWalletConfig {
  address: string
  name?: string
  enabled?: boolean
}

/**
 * Standalone Wallet Tracker Service
 * 
 * This service can track multiple Solana wallets independently of the Telegram bot.
 * It uses multiple Helius API keys for load balancing and unlimited wallet tracking.
 * 
 * Usage:
 * ```typescript
 * const tracker = new StandaloneWalletTracker()
 * 
 * tracker.on('transaction', (event: WalletTransactionEvent) => {
 *   console.log('New transaction:', event)
 * })
 * 
 * await tracker.addWallet({ address: 'wallet_address', name: 'My Wallet' })
 * await tracker.start()
 * ```
 */
export class StandaloneWalletTracker extends EventEmitter {
  private wallets: Map<string, StandaloneWalletConfig> = new Map()
  private subscriptions: Map<string, number> = new Map()
  private walletConnections: Map<string, Connection> = new Map()
  private walletTransactions: Map<string, { count: number; startTime: number }> = new Map()
  private rateLimit: RateLimit
  private isRunning = false

  constructor() {
    super()
    this.rateLimit = new RateLimit(this.subscriptions)
  }

  /**
   * Add a wallet to track
   */
  public async addWallet(config: StandaloneWalletConfig): Promise<void> {
    try {
      // Validate wallet address
      new PublicKey(config.address)
      
      this.wallets.set(config.address, {
        ...config,
        enabled: config.enabled !== false // Default to enabled
      })

      console.log(chalk.greenBright(`Added wallet: ${config.address} (${config.name || 'Unnamed'})`))

      // If tracker is already running, start watching this wallet immediately
      if (this.isRunning) {
        await this.startWatchingWallet(config.address)
      }
    } catch (error) {
      console.error(`Invalid wallet address: ${config.address}`, error)
      throw new Error(`Invalid wallet address: ${config.address}`)
    }
  }

  /**
   * Remove a wallet from tracking
   */
  public async removeWallet(address: string): Promise<void> {
    if (!this.wallets.has(address)) {
      console.warn(`Wallet ${address} not found`)
      return
    }

    await this.stopWatchingWallet(address)
    this.wallets.delete(address)
    console.log(chalk.yellowBright(`Removed wallet: ${address}`))
  }

  /**
   * Enable/disable a wallet
   */
  public async setWalletEnabled(address: string, enabled: boolean): Promise<void> {
    const wallet = this.wallets.get(address)
    if (!wallet) {
      throw new Error(`Wallet ${address} not found`)
    }

    wallet.enabled = enabled

    if (this.isRunning) {
      if (enabled) {
        await this.startWatchingWallet(address)
      } else {
        await this.stopWatchingWallet(address)
      }
    }
  }

  /**
   * Start tracking all enabled wallets
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.warn('Tracker is already running')
      return
    }

    console.log(chalk.blueBright('Starting Standalone Wallet Tracker...'))
    
    // Display connection statistics
    const stats = RpcConnectionManager.getConnectionStats()
    console.log(chalk.cyanBright(`Connection Stats: ${stats.totalRpcEndpoints} RPC endpoints, ${stats.availableHeliusKeys}/${stats.totalHeliusKeys} Helius keys available`))

    this.isRunning = true

    // Start watching all enabled wallets
    for (const [address, config] of this.wallets) {
      if (config.enabled) {
        await this.startWatchingWallet(address)
      }
    }

    console.log(chalk.greenBright(`Started tracking ${this.subscriptions.size} wallets`))
  }

  /**
   * Stop tracking all wallets
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      console.warn('Tracker is not running')
      return
    }

    console.log(chalk.yellowBright('Stopping Standalone Wallet Tracker...'))

    for (const address of this.wallets.keys()) {
      await this.stopWatchingWallet(address)
    }

    this.isRunning = false
    console.log(chalk.redBright('Tracker stopped'))
  }

  /**
   * Get list of tracked wallets
   */
  public getWallets(): StandaloneWalletConfig[] {
    return Array.from(this.wallets.values())
  }

  /**
   * Get tracker statistics
   */
  public getStats() {
    const connectionStats = RpcConnectionManager.getConnectionStats()
    return {
      totalWallets: this.wallets.size,
      activeWallets: this.subscriptions.size,
      enabledWallets: Array.from(this.wallets.values()).filter(w => w.enabled).length,
      isRunning: this.isRunning,
      ...connectionStats
    }
  }

  /**
   * Start watching a specific wallet
   */
  private async startWatchingWallet(address: string): Promise<void> {
    if (this.subscriptions.has(address)) {
      console.log(`Already watching wallet: ${address}`)
      return
    }

    try {
      const publicKey = new PublicKey(address)
      const heliusConnection = RpcConnectionManager.getNextHeliusConnection()
      this.walletConnections.set(address, heliusConnection)

      console.log(chalk.greenBright(`Starting to watch wallet: ${address}`))

      // Initialize transaction tracking
      this.walletTransactions.set(address, { count: 0, startTime: Date.now() })

      const subscriptionId = await this.createSubscription(heliusConnection, publicKey, address)

      if (subscriptionId) {
        this.subscriptions.set(address, subscriptionId)
        console.log(chalk.blueBright(`Subscription created for wallet: ${address} (ID: ${subscriptionId})`))
      } else {
        console.error(`Failed to create subscription for wallet: ${address}`)
      }
    } catch (error) {
      console.error(`Error starting to watch wallet ${address}:`, error)
    }
  }

  /**
   * Stop watching a specific wallet
   */
  private async stopWatchingWallet(address: string): Promise<void> {
    const subscriptionId = this.subscriptions.get(address)
    const connection = this.walletConnections.get(address)

    if (subscriptionId && connection) {
      try {
        await connection.removeOnLogsListener(subscriptionId)
        console.log(chalk.yellowBright(`Stopped watching wallet: ${address}`))
      } catch (error) {
        console.error(`Error stopping wallet watch for ${address}:`, error)
      }
    }

    this.subscriptions.delete(address)
    this.walletConnections.delete(address)
    this.walletTransactions.delete(address)
  }

  /**
   * Create subscription for a wallet
   */
  private async createSubscription(
    connection: Connection,
    publicKey: PublicKey,
    walletAddress: string,
    maxRetries = 3
  ): Promise<number | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const subscriptionId = connection.onLogs(
          publicKey,
          async (logs, ctx) => {
            await this.handleTransaction(logs, walletAddress)
          },
          'processed'
        )

        return subscriptionId
      } catch (error) {
        console.error(`Attempt ${attempt}: Failed to create subscription for ${walletAddress}:`, error)

        if (attempt === maxRetries) {
          RpcConnectionManager.markHeliusKeyAsFailed(connection)
          const newConnection = RpcConnectionManager.getNextHeliusConnection()
          if (newConnection !== connection) {
            this.walletConnections.set(walletAddress, newConnection)
            return this.createSubscription(newConnection, publicKey, walletAddress, 2)
          }
        }

        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }

    return null
  }

  /**
   * Handle incoming transaction
   */
  private async handleTransaction(logs: any, walletAddress: string): Promise<void> {
    try {
      const { isRelevant, swap } = ValidTransactions.isRelevantTransaction(logs)

      if (!isRelevant) {
        return
      }

      // Rate limiting check
      const walletData = this.walletTransactions.get(walletAddress)
      if (!walletData) {
        return
      }

      // Simple rate limiting (you can customize this)
      walletData.count++
      const elapsedTime = (Date.now() - walletData.startTime) / 1000
      if (elapsedTime >= 1) {
        const tps = walletData.count / elapsedTime
        if (tps > 5) { // Configurable threshold
          console.warn(`High TPS detected for wallet ${walletAddress}: ${tps.toFixed(2)}`)
          // Reset counter
          this.walletTransactions.set(walletAddress, { count: 0, startTime: Date.now() })
        }
      }

      const transactionSignature = logs.signature
      const transactionDetails = await this.getParsedTransaction(transactionSignature)

      if (!transactionDetails || transactionDetails[0] === null) {
        return
      }

      // Parse transaction
      const solPriceUsd = CronJobs.getSolPrice()
      const transactionParser = new TransactionParser(transactionSignature)

      let parsed: any = null
      let transactionType: 'buy' | 'sell' | 'transfer' = 'transfer'

      if (
        swap === 'raydium' ||
        swap === 'jupiter' ||
        swap === 'pumpfun' ||
        swap === 'mint_pumpfun' ||
        swap === 'pumpfun_amm'
      ) {
        parsed = await transactionParser.parseDefiTransaction(
          transactionDetails,
          swap,
          solPriceUsd,
          walletAddress,
        )
        transactionType = parsed?.type || 'buy'
      } else if (swap === 'sol_transfer') {
        parsed = await transactionParser.parseSolTransfer(transactionDetails, solPriceUsd, walletAddress)
        transactionType = 'transfer'
      }

      if (parsed) {
        const event: WalletTransactionEvent = {
          walletAddress,
          transactionSignature,
          transactionType,
          tokenAddress: parsed.tokenAddress,
          amount: parsed.amount,
          priceUsd: parsed.priceUsd,
          marketCap: parsed.marketCap,
          description: parsed.description,
          timestamp: Date.now()
        }

        this.emit('transaction', event)
        console.log(chalk.cyanBright(`Transaction detected: ${event.description}`))
      }
    } catch (error) {
      console.error(`Error handling transaction for wallet ${walletAddress}:`, error)
    }
  }

  /**
   * Get parsed transaction with retry logic
   */
  private async getParsedTransaction(transactionSignature: string, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const connection = RpcConnectionManager.getRandomConnection()
        const transactionDetails = await connection.getParsedTransactions(
          [transactionSignature],
          { maxSupportedTransactionVersion: 0 }
        )

        if (transactionDetails && transactionDetails[0] !== null) {
          return transactionDetails
        }
      } catch (error) {
        console.error(`Attempt ${attempt}: Error fetching transaction details`, error)
        if (attempt === retries) {
          // Try with Helius as fallback
          try {
            const heliusConnection = RpcConnectionManager.getRandomHeliusConnection()
            return await heliusConnection.getParsedTransactions(
              [transactionSignature],
              { maxSupportedTransactionVersion: 0 }
            )
          } catch (heliusError) {
            console.error(`Helius fallback failed:`, heliusError)
          }
        }
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }

    return null
  }
}
