<!-- Improved compatibility of back to top link: See: https://github.com/othneildrew/Best-README-Template/pull/73 -->

<a id="readme-top"></a>

<!-- PROJECT LOGO -->
<br />
<div align="center">
  <a href="https://github.com/DracoR22/handi-cat_wallet-tracker">
    <img src="showcase/logo.jpg" alt="Logo" width="80" height="80">
  </a>

  <h3 align="center">🐱 Handi Cat | Wallet Tracker - Enhanced Edition</h3>

  <p align="center">
    Track UNLIMITED Solana wallets in Real-Time with Bulk Operations
    <br />
    <strong>✨ Enhanced with Multiple Helius API Keys, Bulk Management & Advanced Monitoring</strong>
    <br />
    <br />
    <a href="https://t.me/handi_cat_bot"><strong>Use the Telegram bot -></strong></a>
  </p>
</div>

<!-- ABOUT THE PROJECT -->

## About The Project

[![Product Name Screen Shot][product-screenshot]](https://t.me/handi_cat_bot)

**Handi Cat Enhanced Edition** is a powerful Telegram bot that can track **unlimited Solana wallets** in real time with advanced bulk operations and comprehensive monitoring. It provides detailed information for transactions on Raydium, Jupiter, Pump.fun and Pump AMM(PumpSwap) including transaction hash, tokens and amounts swapped, price of the token in SOL, token market cap and much more.

## 🚀 **Enhanced Features**

### **🔥 Unlimited Tracking & Bulk Operations**
- ✅ **Unlimited wallet tracking** (no subscription limits)
- ✅ **Bulk add wallets** - add up to 100 wallets at once
- ✅ **Bulk remove wallets** - remove multiple wallets quickly
- ✅ **Multiple input formats** - address only, with names, comma/space separated
- ✅ **Progress tracking** - real-time progress for bulk operations
- ✅ **Smart validation** - automatic address validation and duplicate detection

### **⚡ Advanced API Management**
- ✅ **Multiple Helius API keys** with automatic load balancing
- ✅ **Automatic failover** when API keys hit rate limits
- ✅ **Multiple RPC endpoints** for redundancy
- ✅ **Connection health monitoring** and statistics
- ✅ **Rate limit protection** with intelligent retry

### **🛡️ Smart Monitoring & Cleanup**
- ✅ **Automatic wallet cleanup** after 5 days of inactivity
- ✅ **24-hour warning notifications** before cleanup
- ✅ **Comprehensive error tracking** with detailed logging
- ✅ **Performance monitoring** and health checks
- ✅ **Admin commands** for system monitoring

### **📊 Core Transaction Tracking**
- ✅ **Real-time SOL transfers** and token swaps
- ✅ **Raydium, Jupiter, Pump.fun and PumpSwap** detection
- ✅ **Token prices in SOL** at time of transaction
- ✅ **Market cap information** for tracked tokens
- ✅ **Token amounts and supply percentage** held by wallets
- ✅ **Trading bot links** for quick token purchases
- ✅ **Chart links** to Photon, GMGN and Dex Screener

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## 🎯 **Bot Commands**

### **📝 Wallet Management**
- `/start` – Opens the bot's main menu
- `/add` – Add a single wallet address
- `/delete` – Delete a single wallet address
- `/bulk_add` – **🔥 Add multiple wallets at once (up to 100)**
- `/bulk_remove` – **🔥 Remove multiple wallets at once**
- `/list_wallets` – View your tracked wallets and count
- `/manage` – Manage all your tracked wallets

### **📊 Monitoring & Admin**
- `/health` – System health status **(admin only)**
- `/errors` – Error summary by component **(admin only)**
- `/logs` – View recent log files **(admin only)**
- `/monitoring_config` – View logging configuration **(admin only)**
- `/cleanup_stats` – Wallet cleanup statistics **(admin only)**
- `/ban_wallet` – Flag a wallet as BANNED **(admin only)**

### **🆘 Help & Information**
- `/help` – Show all available commands
- `/help_notify` – Learn how bot notifications work
- `/help_group` – Instructions for adding the bot to group chats
- `/upgrade` – Access the subscription menu (optional)

## 📦 **How to Use Bulk Operations**

### **🔥 Add Multiple Wallets at Once**

1. **Send the command:**
   ```
   /bulk_add
   ```

2. **Choose your format and send wallet list:**

   **Format 1: Address Only**
   ```
   5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
   7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
   2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S
   ```

   **Format 2: Address with Name (Space Separated)**
   ```
   5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
   7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
   2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S NFT Collector
   ```

   **Format 3: Address with Name (Comma Separated)**
   ```
   5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1,Whale Wallet
   7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi,DeFi Trader
   2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector
   ```

3. **Watch real-time progress:**
   ```
   🔄 Processing 50 wallets...
   📊 Progress: 60%
   🔍 Current: 5Q544fKr...e4j1
   ```

4. **Get detailed results:**
   ```
   📊 Bulk Added Summary
   ✅ Successfully added: 45
   ❌ Failed: 2
   ⏭️ Skipped: 3
   📝 Total processed: 50
   ⏱️ Duration: 12.3s
   ```

### **🗑️ Remove Multiple Wallets at Once**

1. **Send the command:**
   ```
   /bulk_remove
   ```

2. **Send wallet addresses to remove:**
   ```
   5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
   7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
   2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S
   ```

3. **Get removal summary:**
   ```
   📊 Bulk Removed Summary
   ✅ Successfully removed: 23
   ❌ Failed: 0
   ⏭️ Skipped: 2
   📝 Total processed: 25
   ⏱️ Duration: 8.1s
   ```

### **📊 View Your Wallets**
```
/list_wallets
```
Shows your total wallet count and provides quick access to bulk operations.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Built With

- 🌐 Node.JS
- 📘 TypeScript
- 📊 Prisma ORM
- 🪙 Solana Web3.js

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- GETTING STARTED -->

## 🚀 **Getting Started**

Follow these steps to setup **Handi Cat Enhanced Edition** with unlimited wallet tracking

### **Prerequisites**

- **Node.js 14.x or higher**
- **Database** (PostgreSQL/MySQL/MongoDB)
- **Multiple Helius API keys** (recommended: 3-5 keys)
- **Telegram Bot Token**

### **🔧 Enhanced Setup Steps**

1. **Clone the repository**
   ```sh
   git clone https://github.com/DracoR22/handi-cat_wallet-tracker.git
   cd handi-cat_wallet-tracker
   ```

2. **Install dependencies**
   ```sh
   pnpm install
   ```

3. **Configure environment**
   ```sh
   cp .env.example .env
   ```

4. **🔥 Essential Configuration (.env)**
   ```env
   # Multiple Helius API Keys (comma-separated, NO SPACES)
   HELIUS_API_KEYS=key1,key2,key3,key4,key5

   # Multiple RPC Endpoints for load balancing
   RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana

   # Telegram Bot Configuration
   BOT_TOKEN=your_telegram_bot_token_here
   ADMIN_CHAT_ID=your_telegram_chat_id

   # Database (choose one)
   DATABASE_PROVIDER=postgresql
   DATABASE_URL=postgresql://username:password@localhost:5432/wallet_tracker

   # Enhanced Features
   LOG_LEVEL=info
   ENABLE_FILE_LOGGING=true
   ENABLE_ERROR_TRACKING=true
   DISABLE_PAYMENTS=true
   ```

5. **Setup database**
   ```sh
   pnpm db:setup
   ```

6. **✅ Polling Mode (Already Configured)**
   The bot is **already configured** with polling mode for unlimited wallet tracking. No webhook setup needed!

7. **🚀 Start the enhanced bot**
   ```sh
   pnpm start
   ```

   You'll see:
   ```
   🤖 Telegram bot initialized with POLLING mode
   ✅ Perfect for unlimited wallet tracking!
   ```

8. **🎯 Start tracking unlimited wallets**
   - Single wallet: `/add`
   - **Bulk wallets: `/bulk_add`** (up to 100 at once)
   - View wallets: `/list_wallets`
   - System health: `/health`

### **🆓 Free Database Options**
- **PostgreSQL**: [Supabase](https://supabase.com) (500MB free)
- **MySQL**: [PlanetScale](https://planetscale.com) (5GB free)
- **MongoDB**: [MongoDB Atlas](https://www.mongodb.com/atlas) (512MB free)

### **🆓 Free RPC Options**
- **QuickNode**: 100k requests/day free
- **Chainstack**: 100k requests/day free
- **Alchemy**: 300M compute units/month free
- **Ankr**: Unlimited with rate limits

<p align="center"><img src="./showcase/cli-pic.png" width="95%" alt="Screenshot of bot succesfully running"/></>

## 📱 **Enhanced Notification Examples**

### **🟢 Buy Transaction Notification**
```
🟢 BUY DETECTED

💰 Wallet: Whale Wallet
🪙 Token: BONK
💵 Amount: 1,000,000 BONK
💲 Value: $156.78
📈 Price: $0.00015678
🏪 DEX: Raydium
🔗 Transaction: [View on Solscan]

⏰ 2 seconds ago
```

### **🔴 Sell Transaction Notification**
```
🔴 SELL DETECTED

💰 Wallet: DeFi Trader
🪙 Token: SOL
💵 Amount: 10.5 SOL
💲 Value: $1,050.00
📉 Price: $100.00
🏪 DEX: Jupiter
🔗 Transaction: [View on Solscan]

⏰ 1 second ago
```

### **📊 Bulk Operation Result**
```
📊 Bulk Added Summary

✅ Successfully added: 45
❌ Failed: 2
⏭️ Skipped: 3
📝 Total processed: 50
⏱️ Duration: 12.3s

Results:
✅ 5Q544fKr...e4j1 Whale Wallet
✅ 7UX2i7Su...oDUi DeFi Trader
⏭️ 2ojv9BAi...HG8S Already tracking
❌ invalid123...addr Invalid address
```

### **🏥 System Health Status**
```
🏥 System Health Summary

🔗 RPC Connections: 5/5 healthy
🔑 Helius Keys: 4/5 available
👛 Wallets: 150 active, 5 banned
💾 Memory: 65% used
⏱️ Uptime: 24 hours
❌ Errors: 3 last hour, 0 critical

Last updated: 2024-01-15 10:30:00
```

## 🎯 **Performance & Scaling**

### **Capacity Guidelines**

| Setup | Wallets | API Keys | Performance |
|-------|---------|----------|-------------|
| **Starter** | 100-300 | 3 keys | Excellent |
| **Professional** | 500-1000 | 5 keys | Excellent |
| **Enterprise** | 1000+ | 10+ keys | Excellent |

### **🚀 Why This Enhanced Edition?**

- ✅ **No subscription limits** - track unlimited wallets
- ✅ **Bulk operations** - manage hundreds of wallets efficiently
- ✅ **Multiple API keys** - never hit rate limits
- ✅ **Smart monitoring** - comprehensive error tracking
- ✅ **Auto cleanup** - maintains optimal performance
- ✅ **Polling mode** - simple setup, no webhook needed

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- CONTACT -->

## Contact

<!-- [@your_twitter](https://twitter.com/your_username)  --> - <EMAIL>

My solana wallet for the struggles - `5EVQsbVErvJruJvi3v8i3sDSy58GUnGfewwRb8pJk8N1`

Project Link: [https://github.com/DracoR22/handi-cat_wallet-tracker](https://github.com/DracoR22/handi-cat_wallet-tracker)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->

[contributors-shield]: https://img.shields.io/github/contributors/othneildrew/Best-README-Template.svg?style=for-the-badge
[contributors-url]: https://github.com/othneildrew/Best-README-Template/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/othneildrew/Best-README-Template.svg?style=for-the-badge
[forks-url]: https://github.com/othneildrew/Best-README-Template/network/members
[stars-shield]: https://img.shields.io/github/stars/othneildrew/Best-README-Template.svg?style=for-the-badge
[stars-url]: https://github.com/othneildrew/Best-README-Template/stargazers
[issues-shield]: https://img.shields.io/github/issues/othneildrew/Best-README-Template.svg?style=for-the-badge
[issues-url]: https://github.com/othneildrew/Best-README-Template/issues
[license-shield]: https://img.shields.io/github/license/othneildrew/Best-README-Template.svg?style=for-the-badge
[license-url]: https://github.com/othneildrew/Best-README-Template/blob/master/LICENSE.txt
[linkedin-shield]: https://img.shields.io/badge/-LinkedIn-black.svg?style=for-the-badge&logo=linkedin&colorB=555
[linkedin-url]: https://linkedin.com/in/othneildrew
[telegram-bot]: https://img.shields.io/badge/Telegram-2CA5E0?style=for-the-badge&logo=telegram&logoColor=white
[product-screenshot]: showcase/notifications-new.png
