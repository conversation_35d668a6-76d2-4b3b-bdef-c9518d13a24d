import { User, UserSubscription } from '@prisma/client'

// Define enum types as string literals since SQLite doesn't support enums
export type SubscriptionPlan = 'FREE' | 'HOBBY' | 'PRO' | 'WHALE'
export type WalletStatus = 'ACTIVE' | 'USER_PAUSED' | 'SPAM_PAUSED' | 'BANNED'
export type HandiCatStatus = 'ACTIVE' | 'PAUSED'
export type PromotionType = 'UPGRADE_TO_50_WALLETS'

export type UserWallet = {
  wallet: {
    id: string
    address: string
  }
  userId: string
  walletId: string
  name: string
  status: WalletStatus
}

export type UserWithSubscriptionPlan = {
  personalWalletPubKey: string
  userSubscription: {
    plan: string // Changed to string for SQLite compatibility
    subscriptionCurrentPeriodEnd: Date | null
  } | null
}

export type UserPrisma = {
  userSubscription: {
    plan: string // Changed to string for SQLite compatibility
  } | null
  id: string
  hasDonated: boolean
  personalWalletPubKey: string
  personalWalletPrivKey: string
  _count: {
    userWallets: number
  }
} | null
