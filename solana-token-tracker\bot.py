import os
import logging
import time
import asyncio
import json
import requests
import re
from datetime import datetime
from price_tracker import PriceTracker
from helius import (
    is_valid_solana_address,
    get_token_info,
    load_helius_api_keys,
    add_helius_api_key,
    remove_helius_api_key,
    get_api_key_status,
    register_api_key_notification_callback
)

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG
)
logger = logging.getLogger(__name__)

# Initialize price tracker
price_tracker = PriceTracker()

# Telegram Bot API URL
TELEGRAM_API_BASE = "https://api.telegram.org/bot"

def extract_token_address_from_link(text):
    """
    Extract Solana token address from various link formats.
    Returns the token address if found, None otherwise.
    """
    # Common link patterns for Solana tokens
    patterns = [
        # GeckoTerminal format
        r'geckoterminal\.com/solana/(?:pools|tokens)/([A-Za-z0-9]{32,44})',
        # Solscan format
        r'solscan\.io/token/([A-Za-z0-9]{32,44})',
        # Bird<PERSON>ye format
        r'birdeye\.so/token/([A-Za-z0-9]{32,44})',
        # Solana Explorer format
        r'explorer\.solana\.com/address/([A-Za-z0-9]{32,44})',
        # Soul Sniper Bot format
        r't\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{32,44})'
    ]

    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            address = match.group(1)
            if is_valid_solana_address(address):
                logger.info(f"Extracted token address from link: {address}")
                return address

    return None

class SimpleTelegramBot:
    def __init__(self, token):
        self.token = token
        self.api_base = f"{TELEGRAM_API_BASE}{token}"
        self.offset = 0
        self.commands = {
            "/start": self.start_command,
            "/help": self.help_command,
            "/track": self.track_command,
            "/list": self.list_command,
            "/status": self.status_command,
            "/untrack": self.untrack_command,
            "/clear": self.clear_command,
            "/api_keys": self.api_keys_command,
            "/add_api_key": self.add_api_key_command,
            "/remove_api_key": self.remove_api_key_command,
            "/reload_api_keys": self.reload_api_keys_command,
            "/set_trailing_stop": self.set_trailing_stop_command
        }

        # Register API key notification callback
        register_api_key_notification_callback(self.send_api_key_notification)

    def send_message(self, chat_id, text):
        """Send a message to a Telegram chat."""
        url = f"{self.api_base}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": "HTML"
        }
        try:
            response = requests.post(url, json=payload)
            if response.status_code != 200:
                logger.error(f"Failed to send message: {response.text}")
            return response.json()
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return None

    def get_updates(self):
        """Get updates from Telegram Bot API."""
        url = f"{self.api_base}/getUpdates"
        params = {
            "offset": self.offset,
            "timeout": 30
        }
        try:
            response = requests.get(url, params=params)
            if response.status_code != 200:
                logger.error(f"Failed to get updates: {response.text}")
                return []

            updates = response.json()
            if not updates.get("ok"):
                logger.error(f"Failed to get updates: {updates}")
                return []

            result = updates.get("result", [])
            if result:
                # Update offset to acknowledge received updates
                self.offset = result[-1]["update_id"] + 1

            return result
        except Exception as e:
            logger.error(f"Error getting updates: {str(e)}")
            return []

    def start_command(self, update, args):
        """Handle /start command."""
        chat_id = update["message"]["chat"]["id"]
        user = update["message"]["from"]["first_name"]
        text = (
            f"Hi {user}! 👋\n\n"
            f"I'm a Solana token price tracker bot. I can monitor token prices "
            f"and notify you when they reach certain percentage increases or decreases.\n\n"
            f"<b>Quick Start:</b>\n"
            f"• Just paste any Solana token address directly to start tracking!\n"
            f"• Or share any link containing a token address (GeckoTerminal, Solscan, Birdeye, etc.)\n\n"
            f"<b>Token Commands:</b>\n"
            f"/track &lt;token_address&gt; - Start tracking a Solana token\n"
            f"/list - List all tokens you're currently tracking\n"
            f"/status - Check current status and progress of tracked tokens\n"
            f"/status refresh - Update prices of all your tracked tokens\n"
            f"/untrack &lt;token_address&gt; - Stop tracking a token\n"
            f"/clear - Remove all tokens you're tracking\n"
            f"/set_trailing_stop &lt;percentage&gt; - Set trailing stop loss percentage\n"
            f"/help - Show this help message\n\n"
            f"<b>API Key Management:</b>\n"
            f"/api_keys - Show status of all Helius API keys\n"
            f"/add_api_key &lt;key&gt; - Add a new Helius API key\n"
            f"/remove_api_key &lt;key&gt; - Remove a Helius API key\n"
            f"/reload_api_keys - Reload API keys from .env file\n\n"
            f"⏰ <b>Note:</b> All tracked tokens are automatically removed after 24 hours"
        )
        self.send_message(chat_id, text)

    def help_command(self, update, args):
        """Handle /help command."""
        chat_id = update["message"]["chat"]["id"]
        text = (
            "🤖 <b>Solana Token Price Tracker Bot</b> 🤖\n\n"
            "I track Solana token prices and notify you when they reach significant price movements.\n\n"
            "<b>Token Tracking Commands:</b>\n"
            "/start - Start the bot and get welcome message\n"
            "/track &lt;token_address&gt; - Start tracking a Solana token\n"
            "/list - List all tokens you're currently tracking\n"
            "/status - Check current status and progress of tracked tokens\n"
            "/status refresh - Update prices of all your tracked tokens\n"
            "/untrack &lt;token_address&gt; - Stop tracking a token\n"
            "/clear - Remove all tokens you're tracking (requires confirmation)\n"
            "/set_trailing_stop &lt;percentage&gt; - Set trailing stop loss percentage (default: -10%)\n"
            "/help - Show this help message\n\n"

            "<b>API Key Management:</b>\n"
            "/api_keys - Show status of all Helius API keys\n"
            "/add_api_key &lt;key&gt; - Add a new Helius API key\n"
            "/remove_api_key &lt;key&gt; - Remove a Helius API key\n"
            "/reload_api_keys - Reload API keys from .env file\n\n"

            "<b>Quick Tracking:</b>\n"
            "• Simply paste a Solana token address directly to start tracking it\n"
            "• No need to type /track command - just paste the address!\n"
            "• Share any link containing a token address (GeckoTerminal, Solscan, Birdeye, etc.)\n"
            "• I'll automatically extract and track the token address from the link!\n\n"

            "<b>Notifications:</b>\n"
            "You'll receive notifications when a token's price increases by 50%, 100%, 200%, 300%, 400%, 500%, 600%, 700%, 800%, 900%, 1000%, 2000%, 5000%, 7000%, and 10000%.\n"
            "You'll also be notified if the price drops below -35% (unless it reached 50% first). Tokens that drop below -35% will be automatically removed from tracking.\n"
            "You'll receive trailing stop loss notifications when a token's price drops from its peak according to the trailing stop loss formula.\n\n"

            "<b>API Key Features:</b>\n"
            "• You'll receive notifications when API keys are rate limited\n"
            "• You can add new API keys without stopping the bot\n"
            "• The bot automatically rotates between available API keys\n"
            "• Edit the .env file and use /reload_api_keys to update keys\n\n"

            "<b>Important Notes:</b>\n"
            "• All tracked tokens are automatically removed after 24 hours\n"
            "• Prices are checked every 15 seconds for near real-time alerting\n"
            "• Use /status refresh to manually update prices anytime"
        )
        self.send_message(chat_id, text)

    def track_command(self, update, args):
        """Handle /track command."""
        chat_id = update["message"]["chat"]["id"]

        if not args:
            self.send_message(
                chat_id,
                "Please provide a Solana token address.\n"
                "Example: /track EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
            )
            return

        token_address = args[0]

        # Validate the address format
        if not is_valid_solana_address(token_address):
            self.send_message(
                chat_id,
                "❌ Invalid Solana token address format. Please provide a valid address."
            )
            return

        # Try to add the token to the tracker
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(price_tracker.add_token(token_address, chat_id))

        if result["success"]:
            token_name = result["data"]["name"] if result["data"]["name"] else token_address
            initial_price = result["data"]["initial_price"]

            self.send_message(
                chat_id,
                f"✅ Now tracking {token_name} ({token_address})\n"
                f"Initial price: ${initial_price}\n"
                f"You will be notified when the price increases by 50%, 100%, 200%, 300%, 400%, 500%, 600%, 700%, 800%, 900%, 1000%, 2000%, 5000%, 7000%, and 10000%.\n"
                f"You will also be notified if the price drops below -35% (unless it reached 50% first). Tokens that drop below -35% will be automatically removed from tracking.\n"
                f"You will receive trailing stop loss notifications when the token price drops from its peak according to the trailing stop loss formula.\n\n"
                f"⏰ Note: Tokens are automatically removed after 24 hours of tracking."
            )
        else:
            self.send_message(chat_id, f"❌ {result['error']}")

    def list_command(self, update, args):
        """Handle /list command."""
        chat_id = update["message"]["chat"]["id"]
        tokens = price_tracker.get_user_tokens(chat_id)

        if not tokens:
            self.send_message(chat_id, "You are not tracking any tokens yet.")
            return

        current_time = int(time.time())
        message = "🔍 Currently tracking these tokens:\n\n"

        for token_address, token_data in tokens.items():
            name = token_data['name'] if token_data['name'] else token_address
            initial_price = token_data['initial_price']
            current_price = token_data['current_price']
            timestamp = token_data['timestamp']
            tracked_since = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

            # Calculate time until auto-removal (24 hours = 86400 seconds)
            time_left_seconds = (timestamp + 86400) - current_time
            if time_left_seconds <= 0:
                removal_text = "Removal pending..."
            else:
                hours_left = time_left_seconds // 3600
                minutes_left = (time_left_seconds % 3600) // 60
                removal_text = f"Auto-removal in {hours_left}h {minutes_left}m"

            # Calculate percentage change
            if initial_price > 0:
                pct_change = ((current_price - initial_price) / initial_price) * 100
                direction = "📈" if pct_change >= 0 else "📉"
                pct_text = f"{direction} {abs(pct_change):.2f}%"
            else:
                pct_text = "N/A"

            message += f"• <b>{name}</b> ({token_address[:6]}...{token_address[-4:]})\n"
            message += f"  Initial: ${initial_price:.8f}\n"
            message += f"  Current: ${current_price:.8f}\n"
            message += f"  Change: {pct_text}\n"
            message += f"  Tracked since: {tracked_since}\n"
            message += f"  ⏰ {removal_text}\n\n"

        self.send_message(chat_id, message)

    def status_command(self, update, args):
        """Handle /status command."""
        chat_id = update["message"]["chat"]["id"]
        tokens = price_tracker.get_user_tokens(chat_id)

        if not tokens:
            self.send_message(chat_id, "You are not tracking any tokens yet. Use /track <token_address> to start tracking.")
            return

        # Check if it's a refresh request
        is_refresh = False
        if args and args[0].lower() == "refresh":
            is_refresh = True

        # First, fetch current prices for all tokens
        current_time = int(time.time())
        message = "📊 <b>Current Status of Tracked Tokens:</b>\n\n"

        for token_address, token_data in tokens.items():
            # Get latest data from GeckoTerminal
            token_info = get_token_info(token_address)
            name = token_data['name'] if token_data['name'] else token_address
            initial_price = token_data['initial_price']
            timestamp = token_data['timestamp']
            tracked_since = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

            # Calculate time until auto-removal
            time_left_seconds = (timestamp + 86400) - current_time
            if time_left_seconds <= 0:
                removal_text = "Removal pending"
            else:
                hours_left = time_left_seconds // 3600
                minutes_left = (time_left_seconds % 3600) // 60
                removal_text = f"Auto-removal in {hours_left}h {minutes_left}m"

            if token_info["success"]:
                current_price = token_info["data"]["price"]
                price_tracker.tracked_tokens[token_address][chat_id]["current_price"] = current_price
            else:
                current_price = token_data['current_price']

            # Calculate percentage change
            if initial_price > 0:
                pct_change = ((current_price - initial_price) / initial_price) * 100
                if pct_change >= 0:
                    direction = "📈"
                    pct_text = f"{direction} <b>+{pct_change:.2f}%</b>"
                else:
                    direction = "📉"
                    pct_text = f"{direction} <b>{pct_change:.2f}%</b>"

                # Show progress towards next threshold
                next_threshold = None
                progress = 0

                # Find the next threshold to reach
                for threshold in [100, 300, 500, 1000, 2000, 5000, 7000, 10000]:
                    if pct_change < threshold:
                        next_threshold = threshold
                        # If it's the first threshold
                        if threshold == 100:
                            progress = (pct_change / 100) * 100
                        else:
                            # Find the previous threshold
                            prev_thresholds = [0, 100, 300, 500, 1000, 2000, 5000, 7000]
                            for i, t in enumerate([100, 300, 500, 1000, 2000, 5000, 7000, 10000]):
                                if t == threshold:
                                    prev_threshold = prev_thresholds[i]
                                    break
                            progress = ((pct_change - prev_threshold) / (threshold - prev_threshold)) * 100
                        break

                progress_text = ""
                if next_threshold:
                    progress_text = f"\n  Progress to {next_threshold}%: {progress:.1f}% complete"
            else:
                pct_text = "N/A"
                progress_text = ""

            message += f"• <b>{name}</b>\n"
            message += f"  Address: {token_address}\n"
            message += f"  Initial: ${initial_price:.8f}\n"
            message += f"  Current: ${current_price:.8f}\n"
            message += f"  Change: {pct_text}{progress_text}\n"
            message += f"  Tracked since: {tracked_since}\n"
            message += f"  ⏰ {removal_text}\n\n"

        # Add refresh button at the bottom
        refresh_time = datetime.now().strftime('%H:%M:%S')
        message += f"You will be notified when tokens reach 50%, 100%, 200%, 300%, 400%, 500%, 600%, 700%, 800%, 900%, 1000%, 2000%, 5000%, 7000%, and 10000% price increases.\n"
        message += f"You will also be notified if the price drops below -35% (unless it reached 50% first). Tokens that drop below -35% will be automatically removed from tracking.\n"
        message += f"You will receive trailing stop loss notifications when token prices drop from their peak according to the trailing stop loss formula.\n\n"
        message += f"Last updated: {refresh_time}\n"
        message += f"To refresh prices: /status refresh"

        # Show confirmation if this was a refresh
        if is_refresh:
            message += "\n\n🔄 <b>Prices refreshed!</b>"

        self.send_message(chat_id, message)

    def clear_command(self, update, args):
        """Handle /clear command to remove all tracked tokens."""
        chat_id = update["message"]["chat"]["id"]

        # Get current tokens to see if the user is tracking any
        tokens = price_tracker.get_user_tokens(chat_id)
        if not tokens:
            self.send_message(chat_id, "You are not tracking any tokens yet.")
            return

        # Ask for confirmation if no arguments provided
        if not args or args[0].lower() != "confirm":
            token_count = len(tokens)
            token_word = "token" if token_count == 1 else "tokens"
            self.send_message(
                chat_id,
                f"⚠️ <b>Warning:</b> This will remove ALL {token_count} {token_word} you're currently tracking.\n\n"
                f"To confirm, please use: /clear confirm"
            )
            return

        # User confirmed, so clear all tokens
        result = price_tracker.clear_all_tokens(chat_id)

        if result["success"]:
            count = result["count"]
            token_word = "token" if count == 1 else "tokens"
            self.send_message(chat_id, f"✅ Successfully removed all {count} {token_word} from tracking.")
        else:
            self.send_message(chat_id, f"❌ Error clearing tokens: {result.get('error', 'Unknown error')}")

    def untrack_command(self, update, args):
        """Handle /untrack command."""
        chat_id = update["message"]["chat"]["id"]

        if not args:
            self.send_message(
                chat_id,
                "Please provide a Solana token address to untrack.\n"
                "Example: /untrack EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v\n"
                "Use /list to see all tokens you're tracking."
            )
            return

        token_address = args[0]
        result = price_tracker.remove_token(token_address, chat_id)

        if result["success"]:
            self.send_message(chat_id, f"✅ Stopped tracking {token_address}")
        else:
            self.send_message(chat_id, f"❌ {result['error']}")

    def handle_update(self, update):
        """Process a single update from Telegram."""
        logger.info(f"Received update: {json.dumps(update)}")

        if "message" not in update or "text" not in update["message"]:
            logger.debug("Update does not contain a message with text")
            return

        text = update["message"]["text"]
        words = text.split()
        chat_id = update["message"]["chat"]["id"]

        # Check if this is a potential Solana address pasted directly
        if len(words) == 1 and not text.startswith("/") and is_valid_solana_address(text):
            logger.info(f"Direct token address detected: {text}")
            # Call track command with this address
            args = [text]
            self.track_command(update, args)
            return

        # Check if this is a link containing a token address
        token_address = extract_token_address_from_link(text)
        if token_address:
            logger.info(f"Token address extracted from link: {token_address}")
            self.send_message(
                chat_id,
                f"Found Solana token address in your link: <code>{token_address}</code>\n"
                f"Starting to track this token..."
            )
            # Call track command with the extracted address
            args = [token_address]
            self.track_command(update, args)
            return

        command = words[0].split('@')[0]  # Remove bot username from command
        args = words[1:] if len(words) > 1 else []

        logger.info(f"Processing command: {command} with args: {args}")

        if command in self.commands:
            logger.info(f"Executing command handler for {command}")
            self.commands[command](update, args)
        else:
            logger.info(f"Unknown command: {command}")

    def send_api_key_notification(self, message):
        """Send API key notification to all users who have interacted with the bot."""
        # Log the message
        logger.info(f"API Key Notification: {message}")

        # Get all unique chat IDs from tracked tokens
        chat_ids = set()
        for token_address in price_tracker.tracked_tokens:
            for chat_id in price_tracker.tracked_tokens[token_address]:
                chat_ids.add(chat_id)

        # Send notification to all users
        for chat_id in chat_ids:
            try:
                self.send_message(
                    chat_id,
                    f"🔑 <b>API Key Alert</b>\n\n{message}\n\n"
                    f"Use /api_keys to check the status of all API keys."
                )
                logger.info(f"Sent API key notification to chat_id: {chat_id}")
            except Exception as e:
                logger.error(f"Failed to send API key notification to chat_id {chat_id}: {str(e)}")

    def api_keys_command(self, update, args):
        """Handle /api_keys command to show status of all API keys."""
        chat_id = update["message"]["chat"]["id"]

        # Get API key status
        status = get_api_key_status()

        if not status:
            self.send_message(chat_id, "No API keys available.")
            return

        # Format status message
        message = "🔑 <b>Helius API Keys Status</b>\n\n"

        for key_id, key_info in status.items():
            rate_limited = "⚠️ RATE LIMITED" if key_info["rate_limited"] else "✅ OK"
            last_used = datetime.fromtimestamp(key_info["last_used"]).strftime('%Y-%m-%d %H:%M:%S')

            message += f"<b>{key_id}</b> ({key_info['key_preview']}): {rate_limited}\n"
            message += f"Requests: {key_info['requests']}, Errors: {key_info['errors']}\n"
            message += f"Last used: {last_used}\n"

            if key_info["last_error"]:
                error_time = datetime.fromtimestamp(key_info["last_error"]["time"]).strftime('%Y-%m-%d %H:%M:%S')
                message += f"Last error ({error_time}): {key_info['last_error']['message'][:100]}...\n"

            message += "\n"

        message += f"Total API keys: {len(status)}\n\n"
        message += "Commands:\n"
        message += "/add_api_key <key> - Add a new API key\n"
        message += "/remove_api_key <key> - Remove an API key\n"
        message += "/reload_api_keys - Reload API keys from .env file"

        self.send_message(chat_id, message)

    def add_api_key_command(self, update, args):
        """Handle /add_api_key command to add a new API key."""
        chat_id = update["message"]["chat"]["id"]

        if not args or not args[0]:
            self.send_message(
                chat_id,
                "Please provide an API key to add.\n\n"
                "Usage: /add_api_key <your_api_key>"
            )
            return

        api_key = args[0]

        # Basic validation
        if len(api_key) < 20:
            self.send_message(
                chat_id,
                "❌ Invalid API key format. Helius API keys are typically longer than 20 characters."
            )
            return

        # Add the API key
        result = add_helius_api_key(api_key)

        if result:
            self.send_message(
                chat_id,
                f"✅ Successfully added API key: {api_key[:4]}...\n\n"
                f"The key is now in rotation and will be used for API requests."
            )
        else:
            self.send_message(
                chat_id,
                f"❌ Failed to add API key. It may already be in the rotation."
            )

    def remove_api_key_command(self, update, args):
        """Handle /remove_api_key command to remove an API key."""
        chat_id = update["message"]["chat"]["id"]

        if not args or not args[0]:
            self.send_message(
                chat_id,
                "Please provide an API key to remove.\n\n"
                "Usage: /remove_api_key <your_api_key>"
            )
            return

        api_key = args[0]

        # Remove the API key
        result = remove_helius_api_key(api_key)

        if result:
            self.send_message(
                chat_id,
                f"✅ Successfully removed API key: {api_key[:4]}...\n\n"
                f"The key will no longer be used for API requests."
            )
        else:
            self.send_message(
                chat_id,
                f"❌ Failed to remove API key. It may not be in the rotation or it's the only key available."
            )

    def reload_api_keys_command(self, update, args):
        """Handle /reload_api_keys command to reload API keys from .env file."""
        chat_id = update["message"]["chat"]["id"]

        # Reload API keys
        result = load_helius_api_keys()

        self.send_message(
            chat_id,
            f"✅ Reloaded API keys from .env file.\n\n"
            f"Total keys: {result['total_keys']}\n"
            f"New keys: {result['new_keys']}\n"
            f"Removed keys: {result['removed_keys']}\n\n"
            f"Use /api_keys to see the current status."
        )

    def set_trailing_stop_command(self, update, args):
        """Handle /set_trailing_stop command to configure the trailing stop loss percentage."""
        chat_id = update["message"]["chat"]["id"]

        if not args:
            current_percentage = price_tracker.trailing_stop_loss_percentage
            self.send_message(
                chat_id,
                f"Current trailing stop loss percentage is set to {current_percentage}%.\n\n"
                f"To change it, use: /set_trailing_stop <percentage>\n"
                f"Example: /set_trailing_stop -10\n\n"
                f"The percentage should be negative (e.g., -5, -10, -15) and represents the initial stop loss percentage."
            )
            return

        try:
            percentage = float(args[0])

            # Validate the percentage
            if percentage >= 0:
                self.send_message(
                    chat_id,
                    "❌ Trailing stop loss percentage must be negative (e.g., -10)."
                )
                return

            # Update the trailing stop loss percentage
            price_tracker.trailing_stop_loss_percentage = percentage

            self.send_message(
                chat_id,
                f"✅ Trailing stop loss percentage has been set to {percentage}%.\n\n"
                f"This will be used to calculate the trailing stop loss threshold for all tracked tokens."
            )

        except ValueError:
            self.send_message(
                chat_id,
                "❌ Invalid percentage format. Please provide a number (e.g., -10)."
            )

    def poll(self):
        """Poll for updates and process them."""
        while True:
            updates = self.get_updates()
            for update in updates:
                self.handle_update(update)

            # Add a small delay to prevent spamming the API
            time.sleep(1)

def run_bot():
    """Start the bot."""
    # Get the token from environment variable
    token = os.environ.get("TELEGRAM_BOT_TOKEN")

    if not token:
        logger.error("TELEGRAM_BOT_TOKEN environment variable not set!")
        return

    # Create a bot instance
    bot = SimpleTelegramBot(token)

    # Create and set event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # Start the price checking scheduler in the background
    price_tracker.start_price_checking(bot)

    # Start polling for updates
    logger.info("Starting Telegram bot polling")
    try:
        bot.poll()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error running bot: {str(e)}")
