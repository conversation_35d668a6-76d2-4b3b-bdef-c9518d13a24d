import { InlineKeyboardMarkup } from 'node-telegram-bot-api'
import { HOBBY_PLAN_FEE, PRO_PLAN_FEE, WHALE_PLAN_FEE } from '../constants/pricing'
import { HandiCatStatus } from '../types/prisma-types'
import { text } from 'stream/consumers'

export const START_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    // 🔥 ENHANCED BULK OPERATIONS (NEW!)
    [
      { text: '📦 Bulk Add Wallets', callback_data: 'bulk_add' },
      { text: '🗑️ Bulk Remove', callback_data: 'bulk_remove' },
    ],
    // 📊 WALLET MANAGEMENT
    [
      { text: '➕ Add Single Wallet', callback_data: 'add' },
      { text: '📊 List My Wallets', callback_data: 'list_wallets' },
    ],
    [
      { text: '👀 Manage Wallets', callback_data: 'manage' },
      { text: '🗑️ Delete Wallet', callback_data: 'delete' },
    ],
    // 🔧 SYSTEM & SETTINGS
    [
      { text: '👛 My Wallet', callback_data: 'my_wallet' },
      { text: '⚙️ Settings', callback_data: 'settings' },
    ],
    [
      { text: '🆕 Groups', callback_data: 'groups' },
      { text: '🔎 Help', callback_data: 'help' },
    ],
    // 📊 ADMIN & MONITORING (if admin)
    [
      { text: '🏥 System Health', callback_data: 'health' },
      { text: '🚀 RPC Performance', callback_data: 'rpc_performance' },
    ],
    [
      { text: '🚨 Error Monitor', callback_data: 'errors' },
      { text: '🔍 Debug ID', callback_data: 'debug_id' },
    ],
    // 💰 OPTIONAL FEATURES
    [
      { text: '❤️ Donate', callback_data: 'donate' },
      { text: '👑 Upgrade', callback_data: 'upgrade' },
    ],
  ],
}

export const SUB_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [[{ text: '🔙 Back to Main Menu', callback_data: 'back_to_main_menu' }]],
}

export const BACK_BUTTON: InlineKeyboardMarkup = {
  inline_keyboard: [[{ text: '🔙 Back to Main Menu', callback_data: 'back_to_main_menu' }]],
}

export const createTxSubMenu = (tokenSymbol: string, tokenMint: string) => {
  const txSubMenu: InlineKeyboardMarkup = {
    inline_keyboard: [
      [
        {
          text: `🐴 Buy on Trojan: ${tokenSymbol}`,
          url: `https://t.me/solana_trojanbot?start=r-handicatbt-${tokenMint}`,
        },
      ],
      [
        { text: `🐶 BonkBot: ${tokenSymbol}`, url: `https://t.me/bonkbot_bot?start=ref_3au54_ca_${tokenMint}` },
        {
          text: `⭐ Axiom: ${tokenSymbol}`,
          url: `https://axiom.trade/t/${tokenMint}/@handi`,
        },
      ],
      [
        {
          text: `🦖 GMGN: ${tokenSymbol}`,
          url: `https://t.me/GMGN_sol_bot?start=i_kxPdcLKf_c_${tokenMint}`,
        },
      ],
    ],
  }

  return txSubMenu
}

export const MANAGE_SUB_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    // 🔥 BULK OPERATIONS (ENHANCED!)
    [
      { text: '📦 Bulk Add Wallets', callback_data: 'bulk_add' },
      { text: '🗑️ Bulk Remove', callback_data: 'bulk_remove' },
    ],
    // 📊 SINGLE OPERATIONS
    [
      { text: '➕ Add Single Wallet', callback_data: 'add' },
      { text: '🗑️ Delete Single Wallet', callback_data: 'delete' },
    ],
    // 📊 VIEW & MONITOR
    [
      { text: '📊 List All Wallets', callback_data: 'list_wallets' },
      { text: '🧹 Cleanup Stats', callback_data: 'cleanup_stats' },
    ],

    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const UPGRADE_PLAN_SUB_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [
      {
        text: `BUY HOBBY ${HOBBY_PLAN_FEE / 1e9} SOL/m`,
        callback_data: 'upgrade_hobby',
      },
    ],
    [
      {
        text: `BUY PRO ${PRO_PLAN_FEE / 1e9} SOL/m`,
        callback_data: 'upgrade_pro',
      },
    ],
    [
      {
        text: `BUY WHALE ${WHALE_PLAN_FEE / 1e9} SOL/m`,
        callback_data: 'upgrade_whale',
      },
    ],

    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const DONATE_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [{ text: `❤️ ${0.1} SOL`, callback_data: 'donate_action_0.1' }],
    [{ text: `✨ ${0.5} SOL`, callback_data: 'donate_action_0.5' }],
    [{ text: `💪 ${1.0} SOL`, callback_data: 'donate_action_1.0' }],
    [{ text: `🗿 ${5.0} SOL`, callback_data: 'donate_action_5.0' }],
    [{ text: `🔥 ${10.0} SOL`, callback_data: 'donate_action_10.0' }],
    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const SUGGEST_UPGRADE_SUBMENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [{ text: '👑 Upgrade', callback_data: 'upgrade' }],
    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const INSUFFICIENT_BALANCE_SUB_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [{ text: '😺 Your Handi Cat Wallet', callback_data: 'my_wallet' }],
    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const USER_SETTINGS_MENU = (botStatus: HandiCatStatus): InlineKeyboardMarkup => {
  return {
    inline_keyboard: [
      [
        {
          text: `${botStatus === 'ACTIVE' ? '⏸️ Pause Handi Cat' : '▶️ Resume Handi Cat'}`,
          callback_data: 'pause-resume-bot',
        },
      ],
      [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
    ],
  }
}

export const USER_WALLET_SUB_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [
      {
        text: '🔑 Show private key',
        callback_data: 'show_private_key',
      },
    ],
    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}

export const GROUPS_MENU: InlineKeyboardMarkup = {
  inline_keyboard: [
    [
      {
        text: '🗑️ Delete Group',
        callback_data: 'delete_group',
      },
    ],
    [{ text: '🔙 Back', callback_data: 'back_to_main_menu' }],
  ],
}
