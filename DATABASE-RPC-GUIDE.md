# 🗄️ Database & RPC Configuration Guide

This guide explains the database and RPC options for unlimited wallet tracking.

## 🔗 **RPC Endpoints - FREE Options**

### ❌ **Default Solana RPC Limitations**
```
https://api.mainnet-beta.solana.com
```
- **Rate Limit**: ~100 requests per 10 seconds
- **Concurrent Connections**: Very limited
- **Reliability**: Poor during high traffic
- **Suitable for**: Testing only (NOT for unlimited wallets)

### ✅ **FREE RPC Providers (Recommended)**

#### **1. Mix of Free Public RPCs (Best for Starting)**
```env
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com,https://api.metaplex.solana.com
```
- **Cost**: Completely free
- **Limits**: Shared rate limits
- **Suitable for**: 50-200 wallets

#### **2. QuickNode (Free Tier)**
- **Website**: https://www.quicknode.com/endpoints/solana
- **Free Tier**: 100,000 requests/day
- **Setup**:
  1. Create account
  2. Create Solana endpoint
  3. Copy your endpoint URL
```env
RPC_ENDPOINTS=https://your-endpoint.solana-mainnet.quiknode.pro/your-api-key/
```
- **Suitable for**: 200-500 wallets

#### **3. Chainstack (Free Tier)**
- **Website**: https://chainstack.com/build-better-with-solana/
- **Free Tier**: 100,000 requests/day
- **Setup**:
  1. Create account
  2. Deploy Solana node
  3. Get endpoint URL
```env
RPC_ENDPOINTS=https://solana-mainnet.core.chainstack.com/your-api-key
```
- **Suitable for**: 200-500 wallets

#### **4. Alchemy (Free Tier)**
- **Website**: https://www.alchemy.com/solana
- **Free Tier**: 300M compute units/month
- **Setup**:
  1. Create account
  2. Create Solana app
  3. Get API key
```env
RPC_ENDPOINTS=https://solana-mainnet.g.alchemy.com/v2/your-api-key
```
- **Suitable for**: 500-1000 wallets

#### **5. Helius (Free Tier)**
- **Website**: https://www.helius.dev
- **Free Tier**: 100,000 requests/day
- **Note**: You're already using this for logs, can also use for RPC
```env
RPC_ENDPOINTS=https://mainnet.helius-rpc.com/?api-key=your-helius-key
```

### 🚀 **Best FREE Setup (Recommended)**
```env
# Mix multiple free providers for maximum reliability
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com,https://your-quicknode-endpoint.solana-mainnet.quiknode.pro/your-api-key/,https://solana-mainnet.core.chainstack.com/your-api-key
```

## 🗄️ **Database Options**

### **Option 1: PostgreSQL (Recommended)**

#### **FREE PostgreSQL Providers:**

**Supabase (Best Free Option)**
- **Website**: https://supabase.com
- **Free Tier**: 500MB storage, 2 projects
- **Setup**:
  1. Create account
  2. Create new project
  3. Go to Settings > Database
  4. Copy connection string
```env
DATABASE_PROVIDER=postgresql
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

**ElephantSQL**
- **Website**: https://www.elephantsql.com
- **Free Tier**: 20MB storage
- **Good for**: Testing/small deployments

**Aiven**
- **Website**: https://aiven.io
- **Free Tier**: 1 month free trial
- **Good for**: Production testing

### **Option 2: MySQL**

#### **FREE MySQL Providers:**

**PlanetScale (Best MySQL Option)**
- **Website**: https://planetscale.com
- **Free Tier**: 5GB storage, 1 billion reads/month
- **Setup**:
  1. Create account
  2. Create database
  3. Get connection string
```env
DATABASE_PROVIDER=mysql
DATABASE_URL=mysql://username:password@host:3306/database_name?sslaccept=strict
```

**Railway**
- **Website**: https://railway.app
- **Free Tier**: 512MB storage
- **Good for**: Small projects

### **Option 3: MongoDB**

#### **FREE MongoDB Providers:**

**MongoDB Atlas (Best MongoDB Option)**
- **Website**: https://www.mongodb.com/atlas
- **Free Tier**: 512MB storage
- **Setup**:
  1. Create account
  2. Create cluster
  3. Get connection string
  4. Use MongoDB schema
```env
DATABASE_PROVIDER=mongodb
DATABASE_URL=mongodb+srv://username:<EMAIL>/wallet_tracker?retryWrites=true&w=majority
```

**For MongoDB, copy the MongoDB schema:**
```bash
cp prisma/schema.mongodb.prisma prisma/schema.prisma
```

### **Option 4: SQLite (Local)**
```env
DATABASE_PROVIDER=sqlite
DATABASE_URL=file:./wallet_tracker.db
```
- **Good for**: Local development/testing
- **Not recommended**: Production use

## 🛠️ **Setup Instructions**

### **1. Choose Your Configuration**

Edit your `.env` file:
```env
# Example: PostgreSQL + Multiple RPC providers
DATABASE_PROVIDER=postgresql
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://your-quicknode-endpoint.solana-mainnet.quiknode.pro/your-api-key/

HELIUS_API_KEYS=key1,key2,key3,key4,key5
```

### **2. Setup Database**

Use the automated setup script:
```bash
pnpm db:setup
```

Or manually:
```bash
# For PostgreSQL/MySQL/SQLite
pnpm db:migrate

# For MongoDB
pnpm db:push
```

### **3. Verify Setup**

Start the application:
```bash
pnpm start
```

Look for these messages:
```
📡 RPC Endpoints: 5
🔑 Helius API Keys: 5/5 available
💰 Wallet Limits: UNLIMITED
⚡ Load Balancing: ENABLED
```

## 📊 **Performance Expectations**

### **Database Performance:**
- **PostgreSQL**: Best performance, recommended for 1000+ wallets
- **MySQL**: Good performance, suitable for 500+ wallets
- **MongoDB**: Good for document-based queries, 500+ wallets
- **SQLite**: Local only, good for testing

### **RPC Performance:**
- **Single free RPC**: 50-100 wallets max
- **Multiple free RPCs**: 200-500 wallets
- **Paid RPC providers**: 1000+ wallets
- **Mixed setup**: 500-1000 wallets

## 🚨 **Troubleshooting**

### **Common RPC Issues:**

**"Rate limit exceeded"**
- Add more RPC endpoints
- Get API keys from providers
- Reduce tracking frequency

**"Connection timeout"**
- Check RPC endpoint URLs
- Verify API keys are valid
- Try different providers

### **Common Database Issues:**

**"Connection refused"**
- Check DATABASE_URL format
- Verify database is running
- Check firewall settings

**"Table doesn't exist"**
- Run `pnpm db:setup`
- Check DATABASE_PROVIDER matches your database

**"Migration failed"**
- For MongoDB: Use `pnpm db:push` instead
- For others: Check database permissions

## 💡 **Optimization Tips**

### **For Maximum Performance:**
1. **Use multiple RPC providers** (5+ endpoints)
2. **Get API keys** from QuickNode, Chainstack, Alchemy
3. **Use PostgreSQL** for best database performance
4. **Monitor rate limits** and add more providers as needed
5. **Use connection pooling** for high wallet counts

### **Cost-Effective Setup:**
1. **Start with free tiers** from multiple providers
2. **Mix public and private RPCs**
3. **Use Supabase** for free PostgreSQL
4. **Scale up** as you add more wallets

## 🎯 **Recommended Setups**

### **Starter Setup (Free)**
```env
DATABASE_PROVIDER=postgresql
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com
HELIUS_API_KEYS=key1,key2,key3
```
**Capacity**: 200-300 wallets

### **Production Setup (Mixed Free/Paid)**
```env
DATABASE_PROVIDER=postgresql
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://your-quicknode-endpoint.solana-mainnet.quiknode.pro/key/,https://solana-mainnet.core.chainstack.com/key,https://solana-mainnet.g.alchemy.com/v2/key
HELIUS_API_KEYS=key1,key2,key3,key4,key5
```
**Capacity**: 1000+ wallets

This setup gives you unlimited wallet tracking with reliable, free infrastructure! 🚀
