import logging
import requests
import re
import os
import json
import time
import random
import threading
from typing import Dict, Any, Optional, List, Set, Callable

# Set up logging
logger = logging.getLogger(__name__)

# List to store multiple Helius API keys
HELIUS_API_KEYS = []

# Dictionary to track API key usage and rate limits
API_KEY_STATUS = {}

# Lock for thread-safe operations on API keys
api_key_lock = threading.Lock()

# Callback function for API key notifications
api_key_notification_callback = None

def load_helius_api_keys():
    """Load Helius API keys from environment variables and .env file."""
    global HELIUS_API_KEYS, API_KEY_STATUS

    with api_key_lock:
        # Clear existing keys
        old_keys = set(HELIUS_API_KEYS)
        HELIUS_API_KEYS = []

        # Load primary Helius API key from environment variables
        primary_key = os.environ.get("HELIUS_API_KEY", "")
        if primary_key:
            HELIUS_API_KEYS.append(primary_key)
            logger.info(f"Loaded primary Helius API key: {primary_key[:4]}...")

            # Initialize status for this key if it's new
            if primary_key not in API_KEY_STATUS:
                API_KEY_STATUS[primary_key] = {
                    "requests": 0,
                    "errors": 0,
                    "rate_limited": False,
                    "last_used": time.time(),
                    "last_error": None
                }

        # Load additional Helius API keys if available
        for i in range(1, 11):  # Support up to 10 additional keys
            key_name = f"HELIUS_API_KEY_{i}"
            additional_key = os.environ.get(key_name, "")
            if additional_key:
                HELIUS_API_KEYS.append(additional_key)
                logger.info(f"Loaded additional Helius API key {i}: {additional_key[:4]}...")

                # Initialize status for this key if it's new
                if additional_key not in API_KEY_STATUS:
                    API_KEY_STATUS[additional_key] = {
                        "requests": 0,
                        "errors": 0,
                        "rate_limited": False,
                        "last_used": time.time(),
                        "last_error": None
                    }

        # If no keys found in environment variables, try loading from .env file
        if not HELIUS_API_KEYS:
            logger.warning("No Helius API keys found in environment variables")
            try:
                from dotenv import load_dotenv
                load_dotenv(override=True)

                # Try loading primary key again
                primary_key = os.environ.get("HELIUS_API_KEY", "")
                if primary_key:
                    HELIUS_API_KEYS.append(primary_key)
                    logger.info(f"Loaded primary Helius API key from .env file: {primary_key[:4]}...")

                    # Initialize status for this key if it's new
                    if primary_key not in API_KEY_STATUS:
                        API_KEY_STATUS[primary_key] = {
                            "requests": 0,
                            "errors": 0,
                            "rate_limited": False,
                            "last_used": time.time(),
                            "last_error": None
                        }

                # Try loading additional keys
                for i in range(1, 11):
                    key_name = f"HELIUS_API_KEY_{i}"
                    additional_key = os.environ.get(key_name, "")
                    if additional_key:
                        HELIUS_API_KEYS.append(additional_key)
                        logger.info(f"Loaded additional Helius API key {i} from .env file: {additional_key[:4]}...")

                        # Initialize status for this key if it's new
                        if additional_key not in API_KEY_STATUS:
                            API_KEY_STATUS[additional_key] = {
                                "requests": 0,
                                "errors": 0,
                                "rate_limited": False,
                                "last_used": time.time(),
                                "last_error": None
                            }
            except Exception as e:
                logger.error(f"Failed to load Helius API keys from .env file: {str(e)}")

        # As a last resort, use the hardcoded API key
        if not HELIUS_API_KEYS:
            fallback_key = "cce0c2a0-19ba-45a6-a848-ef330347ae59"
            HELIUS_API_KEYS.append(fallback_key)
            logger.info(f"Using hardcoded Helius API key: {fallback_key[:4]}...")

            # Initialize status for this key if it's new
            if fallback_key not in API_KEY_STATUS:
                API_KEY_STATUS[fallback_key] = {
                    "requests": 0,
                    "errors": 0,
                    "rate_limited": False,
                    "last_used": time.time(),
                    "last_error": None
                }

        # Clean up status for keys that are no longer used
        current_keys = set(HELIUS_API_KEYS)
        new_keys = current_keys - old_keys
        removed_keys = old_keys - current_keys

        # Log changes
        if new_keys:
            logger.info(f"Added {len(new_keys)} new API key(s)")
        if removed_keys:
            logger.info(f"Removed {len(removed_keys)} API key(s)")

            # Remove status for keys that are no longer used
            for key in removed_keys:
                if key in API_KEY_STATUS:
                    del API_KEY_STATUS[key]

        logger.info(f"Total Helius API keys available: {len(HELIUS_API_KEYS)}")
        return {
            "total_keys": len(HELIUS_API_KEYS),
            "new_keys": len(new_keys),
            "removed_keys": len(removed_keys)
        }

# Load API keys initially
load_helius_api_keys()

def register_api_key_notification_callback(callback: Callable):
    """Register a callback function to be called when an API key is rate limited."""
    global api_key_notification_callback
    api_key_notification_callback = callback
    logger.info("Registered API key notification callback")

def add_helius_api_key(api_key: str) -> bool:
    """Add a new Helius API key to the rotation."""
    global HELIUS_API_KEYS, API_KEY_STATUS

    with api_key_lock:
        # Check if key is already in the list
        if api_key in HELIUS_API_KEYS:
            logger.warning(f"API key {api_key[:4]}... is already in the rotation")
            return False

        # Add the key to the list
        HELIUS_API_KEYS.append(api_key)

        # Initialize status for this key
        API_KEY_STATUS[api_key] = {
            "requests": 0,
            "errors": 0,
            "rate_limited": False,
            "last_used": time.time(),
            "last_error": None
        }

        logger.info(f"Added new Helius API key: {api_key[:4]}...")
        logger.info(f"Total Helius API keys available: {len(HELIUS_API_KEYS)}")
        return True

def remove_helius_api_key(api_key: str) -> bool:
    """Remove a Helius API key from the rotation."""
    global HELIUS_API_KEYS, API_KEY_STATUS

    with api_key_lock:
        # Check if key is in the list
        if api_key not in HELIUS_API_KEYS:
            logger.warning(f"API key {api_key[:4]}... is not in the rotation")
            return False

        # Check if this is the only key
        if len(HELIUS_API_KEYS) <= 1:
            logger.warning("Cannot remove the only API key")
            return False

        # Remove the key from the list
        HELIUS_API_KEYS.remove(api_key)

        # Remove status for this key
        if api_key in API_KEY_STATUS:
            del API_KEY_STATUS[api_key]

        logger.info(f"Removed Helius API key: {api_key[:4]}...")
        logger.info(f"Total Helius API keys available: {len(HELIUS_API_KEYS)}")
        return True

def get_api_key_status() -> Dict[str, Any]:
    """Get status of all API keys."""
    with api_key_lock:
        status = {}
        for i, key in enumerate(HELIUS_API_KEYS):
            key_id = f"HELIUS_API_KEY{'' if i == 0 else f'_{i}'}"
            key_status = API_KEY_STATUS.get(key, {})
            status[key_id] = {
                "key_preview": f"{key[:4]}...",
                "requests": key_status.get("requests", 0),
                "errors": key_status.get("errors", 0),
                "rate_limited": key_status.get("rate_limited", False),
                "last_used": key_status.get("last_used", 0),
                "last_error": key_status.get("last_error", None)
            }
        return status

def get_helius_api_key() -> str:
    """Get a Helius API key from the available keys, prioritizing keys that aren't rate limited."""
    with api_key_lock:
        # Filter out rate-limited keys if possible
        available_keys = [k for k in HELIUS_API_KEYS if not API_KEY_STATUS.get(k, {}).get("rate_limited", False)]

        # If all keys are rate-limited, use all keys
        if not available_keys:
            logger.warning("All API keys are rate-limited, using all keys")
            available_keys = HELIUS_API_KEYS

        # Select a random key from available keys
        api_key = random.choice(available_keys)

        # Update last used time
        if api_key in API_KEY_STATUS:
            API_KEY_STATUS[api_key]["last_used"] = time.time()

        return api_key

def get_helius_api_url() -> str:
    """Get Helius API URL with a selected API key."""
    api_key = get_helius_api_key()
    return f"https://mainnet.helius-rpc.com/?api-key={api_key}"

def handle_api_key_error(api_key: str, error_type: str, error_message: str):
    """Handle API key errors, including rate limiting."""
    global API_KEY_STATUS, api_key_notification_callback

    with api_key_lock:
        if api_key not in API_KEY_STATUS:
            API_KEY_STATUS[api_key] = {
                "requests": 0,
                "errors": 0,
                "rate_limited": False,
                "last_used": time.time(),
                "last_error": None
            }

        # Update error count
        API_KEY_STATUS[api_key]["errors"] = API_KEY_STATUS[api_key].get("errors", 0) + 1
        API_KEY_STATUS[api_key]["last_error"] = {
            "type": error_type,
            "message": error_message,
            "time": time.time()
        }

        # Check if this is a rate limit error
        is_rate_limit = (
            "rate limit" in error_message.lower() or
            "too many requests" in error_message.lower() or
            "429" in error_message
        )

        if is_rate_limit:
            logger.warning(f"API key {api_key[:4]}... has been rate limited: {error_message}")
            API_KEY_STATUS[api_key]["rate_limited"] = True

            # Call notification callback if registered
            if api_key_notification_callback:
                try:
                    api_key_notification_callback(
                        f"⚠️ Helius API key {api_key[:4]}... has been rate limited.\n"
                        f"Error: {error_message}\n"
                        f"Total available keys: {len(HELIUS_API_KEYS)}"
                    )
                except Exception as e:
                    logger.error(f"Error calling API key notification callback: {str(e)}")
        else:
            logger.error(f"API key {api_key[:4]}... error: {error_type} - {error_message}")

def update_api_key_request_count(api_key: str):
    """Update request count for an API key."""
    global API_KEY_STATUS

    with api_key_lock:
        if api_key not in API_KEY_STATUS:
            API_KEY_STATUS[api_key] = {
                "requests": 0,
                "errors": 0,
                "rate_limited": False,
                "last_used": time.time(),
                "last_error": None
            }

        # Update request count
        API_KEY_STATUS[api_key]["requests"] = API_KEY_STATUS[api_key].get("requests", 0) + 1
        API_KEY_STATUS[api_key]["last_used"] = time.time()

# List of DexScreener API endpoints in order of preference
DEXSCREENER_ENDPOINTS = [
    "https://api.dexscreener.com/latest/dex/tokens/",  # Primary endpoint - most reliable
    "https://api.dexscreener.io/latest/dex/tokens/",   # Alternative endpoint 1
    "https://api.dexscreener.com/v1/dex/tokens/",      # Alternative endpoint 2
    "https://api.dexscreener.io/v1/dex/tokens/"        # Alternative endpoint 3
]

# Function to get the primary DexScreener API endpoint
def get_dexscreener_endpoint() -> str:
    """Get the primary DexScreener API endpoint."""
    return DEXSCREENER_ENDPOINTS[0]  # Always use the primary endpoint first

def is_valid_solana_address(address: str) -> bool:
    """
    Check if a string is a valid Solana address format.
    Also handles PumpFun tokens that end with 'pump'.
    """
    # Basic validation
    if not address or not isinstance(address, str):
        return False

    # Check if this is a PumpFun token (ends with "pump")
    if address.lower().endswith("pump"):
        # For PumpFun tokens, check the part before "pump"
        base_address = address[:-4]  # Remove "pump" suffix

        # Log the base address for debugging
        logger.debug(f"Checking PumpFun token base address: {base_address} (length: {len(base_address)})")

        # Solana addresses are base58 encoded and typically 32-44 characters
        # For PumpFun tokens, we'll be more lenient with the length
        if len(base_address) < 25 or len(base_address) > 50:
            logger.debug(f"PumpFun token base address length invalid: {len(base_address)}")
            return False

        # Check if it contains only base58 characters (alphanumeric without 0, O, I, l)
        base58_pattern = re.compile(r'^[1-9A-HJ-NP-Za-km-z]+$')
        is_valid = bool(base58_pattern.match(base_address))

        logger.debug(f"PumpFun token base address valid: {is_valid}")
        return is_valid
    else:
        # Standard Solana address check
        if len(address) < 32 or len(address) > 44:
            return False

        # Check if it contains only base58 characters (alphanumeric without 0, O, I, l)
        base58_pattern = re.compile(r'^[1-9A-HJ-NP-Za-km-z]+$')
        return bool(base58_pattern.match(address))

def get_token_info(token_address: str) -> Dict[str, Any]:
    """
    Get token information from Helius API.

    Args:
        token_address: Solana token address

    Returns:
        Dictionary with token information or error
    """
    if not is_valid_solana_address(token_address):
        return {
            "success": False,
            "error": "Invalid Solana token address format"
        }

    # Log the token address we're trying to fetch
    logger.info(f"Fetching token info for: {token_address}")

    # Check if this is a PumpFun token (ends with "pump")
    is_pumpfun = token_address.lower().endswith("pump")
    if is_pumpfun:
        logger.info(f"Detected PumpFun token: {token_address}")
        # For PumpFun tokens, use the specialized function
        return get_pumpfun_token_info(token_address)

    # For regular tokens, use Helius API
    payload = {
        "jsonrpc": "2.0",
        "id": "helius-token-info-request",
        "method": "getAsset",
        "params": {
            "id": token_address,
            "displayOptions": {
                "showFungible": True
            }
        }
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        api_key = get_helius_api_key()
        helius_api_url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"
        update_api_key_request_count(api_key)
        response = requests.post(helius_api_url, json=payload, headers=headers)

        # Check if request was successful
        if response.status_code == 200:
            data = response.json()

            # Log the full response for debugging
            logger.info(f"Helius API response: {json.dumps(data)[:500]}...")

            # Check if result exists
            if "result" in data and data["result"]:
                result = data["result"]

                # Extract name and symbol
                name = ""
                symbol = ""

                # Try to get metadata from content.metadata first
                if "content" in result and "metadata" in result["content"]:
                    metadata = result["content"]["metadata"]
                    name = metadata.get("name", "")
                    symbol = metadata.get("symbol", "")
                    logger.info(f"Found metadata in content.metadata: name={name}, symbol={symbol}")

                # If not found in content.metadata, try token_info
                if (not name or not symbol) and "token_info" in result:
                    token_info = result["token_info"]
                    if not symbol:
                        symbol = token_info.get("symbol", "")
                    if not name:
                        name = symbol
                    logger.info(f"Found metadata in token_info: name={name}, symbol={symbol}")

                # If still not found, use default values
                if not name:
                    name = f"Token {token_address[:6]}...{token_address[-4:]}"
                if not symbol:
                    symbol = "UNKNOWN"

                # Extract price
                price = 0.0

                # Try different price fields that might be in the response
                if "token_info" in result:
                    token_info = result["token_info"]
                    logger.info(f"Token info: {json.dumps(token_info)[:500]}...")

                    # Try price_info field
                    if "price_info" in token_info:
                        price_info = token_info["price_info"]
                        price = price_info.get("price_per_token", 0.0)
                        logger.info(f"Found price in price_info: {price}")

                    # Try price field directly
                    elif "price" in token_info:
                        price = token_info.get("price", 0.0)
                        logger.info(f"Found price directly in token_info: {price}")

                # Try other possible locations for price
                if price == 0.0 and "price" in result:
                    price = result.get("price", 0.0)
                    logger.info(f"Found price directly in result: {price}")

                # If price is still 0, we'll try DexScreener API later

                # If price is still 0, try DexScreener API as a last resort
                if price == 0.0:
                    logger.info(f"Price is still 0, trying DexScreener API for token: {token_address}")

                    # Get a random DexScreener API endpoint
                    dexscreener_base_url = get_dexscreener_endpoint()
                    dexscreener_url = f"{dexscreener_base_url}{token_address}"

                    try:
                        logger.info(f"Using DexScreener endpoint: {dexscreener_base_url}")
                        dexscreener_response = requests.get(dexscreener_url)

                        if dexscreener_response.status_code == 200:
                            dexscreener_data = dexscreener_response.json()

                            # Check if pairs data exists
                            if "pairs" in dexscreener_data and len(dexscreener_data["pairs"]) > 0:
                                # Get the first pair
                                pair = dexscreener_data["pairs"][0]

                                # Get the price
                                if "priceUsd" in pair:
                                    price = float(pair["priceUsd"])
                                    logger.info(f"Found price from DexScreener API: {price}")
                    except Exception as e:
                        logger.error(f"Error fetching price from DexScreener API: {str(e)}")

                return {
                    "success": True,
                    "data": {
                        "address": token_address,
                        "name": name,
                        "symbol": symbol,
                        "price": price,
                        "initial_price": price  # Set initial price to current price for new tokens
                    }
                }
            else:
                # If we get here, we couldn't find the token
                error_msg = data.get("error", {}).get("message", "Token not found")
                logger.warning(f"Token not found: {token_address}. Error: {error_msg}")

                # Return a default response with zero price
                return {
                    "success": True,
                    "data": {
                        "address": token_address,
                        "name": f"Token {token_address[:6]}...{token_address[-4:]}",
                        "symbol": "UNKNOWN",
                        "price": 0.0,
                        "initial_price": 0.0
                    }
                }
        else:
            error_message = f"Failed to get token info from Helius: {response.status_code} - {response.text}"
            logger.error(error_message)

            # Handle API key error
            handle_api_key_error(api_key, "HTTP_ERROR", error_message)

            # Return a default response with zero price
            return {
                "success": True,
                "data": {
                    "address": token_address,
                    "name": f"Token {token_address[:6]}...{token_address[-4:]}",
                    "symbol": "UNKNOWN",
                    "price": 0.0,
                    "initial_price": 0.0
                }
            }
    except Exception as e:
        error_message = f"Error fetching token info from Helius: {str(e)}"
        logger.error(error_message)

        # Handle API key error
        handle_api_key_error(api_key, "EXCEPTION", error_message)

        # Return a default response with zero price
        return {
            "success": True,
            "data": {
                "address": token_address,
                "name": f"Token {token_address[:6]}...{token_address[-4:]}",
                "symbol": "UNKNOWN",
                "price": 0.0,
                "initial_price": 0.0
            }
        }



def get_pumpfun_token_info(token_address: str) -> Dict[str, Any]:
    """
    Get token information for PumpFun tokens.

    Args:
        token_address: Solana token address (ending with 'pump')

    Returns:
        Dictionary with token information or error
    """
    # For PumpFun tokens, we need to use DexScreener API which has the best support for them

    # First, try to get metadata from Helius API
    payload = {
        "jsonrpc": "2.0",
        "id": "helius-token-info-request",
        "method": "getAsset",
        "params": {
            "id": token_address,
            "displayOptions": {
                "showFungible": True
            }
        }
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        logger.info(f"Fetching PumpFun token info from Helius API for: {token_address}")
        api_key = get_helius_api_key()
        helius_api_url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"
        update_api_key_request_count(api_key)
        response = requests.post(helius_api_url, json=payload, headers=headers)

        # Extract name and symbol from Helius API
        name = ""
        symbol = ""

        if response.status_code == 200:
            data = response.json()

            if "result" in data and data["result"]:
                result = data["result"]

                # Try to get metadata from content.metadata first
                if "content" in result and "metadata" in result["content"]:
                    metadata = result["content"]["metadata"]
                    name = metadata.get("name", "")
                    symbol = metadata.get("symbol", "")

                # If not found in content.metadata, try token_info
                if (not name or not symbol) and "token_info" in result:
                    token_info = result["token_info"]
                    if not symbol:
                        symbol = token_info.get("symbol", "")
                    if not name:
                        name = symbol

        # If name or symbol not found, use default values
        if not name:
            name = f"PumpFun Token {token_address[:6]}...{token_address[-8:]}"
        if not symbol:
            symbol = "PUMP"

        # For PumpFun tokens, DexScreener API is the most reliable source for price data
        price = 0.0

        # Try DexScreener API
        logger.info(f"Fetching price for PumpFun token from DexScreener API: {token_address}")
        dexscreener_base_url = get_dexscreener_endpoint()
        dexscreener_url = f"{dexscreener_base_url}{token_address}"

        try:
            logger.info(f"Using DexScreener endpoint for PumpFun token: {dexscreener_base_url}")
            dexscreener_response = requests.get(dexscreener_url)

            if dexscreener_response.status_code == 200:
                dexscreener_data = dexscreener_response.json()

                # Check if pairs data exists
                if "pairs" in dexscreener_data and len(dexscreener_data["pairs"]) > 0:
                    # Get the first pair
                    pair = dexscreener_data["pairs"][0]

                    # Get the price
                    if "priceUsd" in pair:
                        price = float(pair["priceUsd"])
                        logger.info(f"Found price from DexScreener API: {price}")

                        # Also try to get name and symbol if not found from Helius
                        if not name and "baseToken" in pair and "name" in pair["baseToken"]:
                            name = pair["baseToken"]["name"]
                        if not symbol and "baseToken" in pair and "symbol" in pair["baseToken"]:
                            symbol = pair["baseToken"]["symbol"]
        except Exception as e:
            logger.error(f"Error fetching price from DexScreener API: {str(e)}")

        # Try all DexScreener endpoints in sequence if price is still 0
        if price == 0.0:
            logger.info(f"Price is still 0, trying all DexScreener endpoints for token: {token_address}")

            # Try each endpoint in order
            for endpoint in DEXSCREENER_ENDPOINTS:
                # Skip the endpoint we already tried
                if endpoint == dexscreener_base_url:
                    continue

                endpoint_url = f"{endpoint}{token_address}"

                try:
                    logger.info(f"Using alternative DexScreener endpoint: {endpoint}")
                    endpoint_response = requests.get(endpoint_url)

                    if endpoint_response.status_code == 200:
                        endpoint_data = endpoint_response.json()

                        if "pairs" in endpoint_data and len(endpoint_data["pairs"]) > 0:
                            pair = endpoint_data["pairs"][0]

                            if "priceUsd" in pair:
                                price = float(pair["priceUsd"])
                                logger.info(f"Found price from alternative DexScreener API: {price}")
                                # If we found a price, break out of the loop
                                break
                except Exception as e:
                    logger.error(f"Error fetching price from DexScreener endpoint {endpoint}: {str(e)}")
                    # Continue to the next endpoint

        return {
            "success": True,
            "data": {
                "address": token_address,
                "name": name,
                "symbol": symbol,
                "price": price,
                "initial_price": price  # Set initial price to current price for new tokens
            }
        }
    except Exception as e:
        error_message = f"Error fetching PumpFun token info: {str(e)}"
        logger.error(error_message)

        # Handle API key error
        handle_api_key_error(api_key, "EXCEPTION", error_message)

        return {
            "success": True,
            "data": {
                "address": token_address,
                "name": f"PumpFun Token {token_address[:6]}...{token_address[-8:]}",
                "symbol": "PUMP",
                "price": 0.0,
                "initial_price": 0.0
            }
        }
