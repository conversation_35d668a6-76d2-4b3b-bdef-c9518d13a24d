import { PrismaWalletRepository } from '../repositories/prisma/wallet'
import { TrackWallets } from '../lib/track-wallets'
import { bot } from '../providers/telegram'
import chalk from 'chalk'
import cron from 'node-cron'

export interface WalletCleanupConfig {
  maxAgeInDays: number
  enableNotifications: boolean
  dryRun: boolean
  batchSize: number
}

export interface CleanupResult {
  totalWalletsChecked: number
  walletsRemoved: number
  usersNotified: number
  errors: string[]
}

/**
 * Wallet Cleanup Service
 * 
 * Automatically removes wallets that are older than the specified age.
 * Provides notifications to users before removal and detailed logging.
 */
export class WalletCleanupService {
  private prismaWalletRepository: PrismaWalletRepository
  private trackWallets: TrackWallets
  private config: WalletCleanupConfig

  constructor(config: Partial<WalletCleanupConfig> = {}) {
    this.prismaWalletRepository = new PrismaWalletRepository()
    this.trackWallets = new TrackWallets()
    
    // Default configuration
    this.config = {
      maxAgeInDays: 5,
      enableNotifications: true,
      dryRun: false,
      batchSize: 100,
      ...config
    }

    console.log(chalk.blueBright('🧹 Wallet Cleanup Service initialized'))
    console.log(chalk.cyanBright(`Configuration: ${JSON.stringify(this.config, null, 2)}`))
  }

  /**
   * Start automatic cleanup with cron job
   * Runs daily at 2 AM
   */
  public startAutomaticCleanup(): void {
    console.log(chalk.greenBright('🕐 Starting automatic wallet cleanup (daily at 2 AM)'))
    
    // Run daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      console.log(chalk.yellowBright('🧹 Running scheduled wallet cleanup...'))
      try {
        const result = await this.cleanupOldWallets()
        console.log(chalk.greenBright('✅ Scheduled cleanup completed:'), result)
      } catch (error) {
        console.error(chalk.redBright('❌ Scheduled cleanup failed:'), error)
      }
    })

    // Also run a warning notification 1 day before cleanup (1 AM)
    cron.schedule('0 1 * * *', async () => {
      console.log(chalk.yellowBright('⚠️  Running wallet expiration warnings...'))
      try {
        await this.sendExpirationWarnings()
      } catch (error) {
        console.error(chalk.redBright('❌ Warning notifications failed:'), error)
      }
    })
  }

  /**
   * Manually run wallet cleanup
   */
  public async cleanupOldWallets(): Promise<CleanupResult> {
    const result: CleanupResult = {
      totalWalletsChecked: 0,
      walletsRemoved: 0,
      usersNotified: 0,
      errors: []
    }

    try {
      console.log(chalk.blueBright(`🔍 Searching for wallets older than ${this.config.maxAgeInDays} days...`))

      // Calculate cutoff date
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - this.config.maxAgeInDays)

      // Get old wallets in batches
      let offset = 0
      let hasMore = true

      while (hasMore) {
        const oldWallets = await this.getOldWallets(cutoffDate, offset, this.config.batchSize)
        
        if (oldWallets.length === 0) {
          hasMore = false
          break
        }

        result.totalWalletsChecked += oldWallets.length

        console.log(chalk.cyanBright(`📦 Processing batch of ${oldWallets.length} old wallets...`))

        for (const userWallet of oldWallets) {
          try {
            if (this.config.dryRun) {
              console.log(chalk.yellowBright(`[DRY RUN] Would remove wallet: ${userWallet.address} (${userWallet.name}) for user ${userWallet.userId}`))
              result.walletsRemoved++
            } else {
              // Send notification to user
              if (this.config.enableNotifications) {
                await this.notifyUserOfRemoval(userWallet)
                result.usersNotified++
              }

              // Remove wallet from tracking
              await this.trackWallets.stopWatchingWallet(userWallet.walletId)

              // Remove from database
              await this.prismaWalletRepository.deleteUserWallet(userWallet.userId, userWallet.walletId)

              console.log(chalk.greenBright(`✅ Removed wallet: ${userWallet.address} (${userWallet.name}) for user ${userWallet.userId}`))
              result.walletsRemoved++
            }
          } catch (error) {
            const errorMsg = `Failed to remove wallet ${userWallet.address}: ${error}`
            console.error(chalk.redBright('❌'), errorMsg)
            result.errors.push(errorMsg)
          }
        }

        offset += this.config.batchSize

        // Small delay between batches to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      console.log(chalk.greenBright('🎉 Wallet cleanup completed!'))
      console.log(chalk.cyanBright(`📊 Results: ${JSON.stringify(result, null, 2)}`))

      return result

    } catch (error) {
      console.error(chalk.redBright('❌ Wallet cleanup failed:'), error)
      result.errors.push(`Cleanup failed: ${error}`)
      return result
    }
  }

  /**
   * Send expiration warnings to users (1 day before cleanup)
   */
  public async sendExpirationWarnings(): Promise<void> {
    try {
      // Calculate warning date (1 day before cleanup)
      const warningDate = new Date()
      warningDate.setDate(warningDate.getDate() - (this.config.maxAgeInDays - 1))

      const walletsToWarn = await this.getOldWallets(warningDate, 0, 1000)

      console.log(chalk.yellowBright(`⚠️  Found ${walletsToWarn.length} wallets expiring soon`))

      for (const userWallet of walletsToWarn) {
        try {
          await this.notifyUserOfExpiration(userWallet)
          console.log(chalk.cyanBright(`📨 Sent expiration warning to user ${userWallet.userId} for wallet ${userWallet.address}`))
        } catch (error) {
          console.error(chalk.redBright(`❌ Failed to send warning to user ${userWallet.userId}:`, error))
        }
      }

    } catch (error) {
      console.error(chalk.redBright('❌ Failed to send expiration warnings:'), error)
    }
  }

  /**
   * Get wallets older than the specified date
   */
  private async getOldWallets(cutoffDate: Date, offset: number, limit: number) {
    // This method needs to be implemented in PrismaWalletRepository
    // For now, I'll create a basic implementation
    return await this.prismaWalletRepository.getWalletsOlderThan(cutoffDate, offset, limit)
  }

  /**
   * Notify user of wallet removal
   */
  private async notifyUserOfRemoval(userWallet: any): Promise<void> {
    if (!this.config.enableNotifications) return

    const message = `
🧹 <b>Wallet Automatically Removed</b>

Your wallet has been automatically removed after ${this.config.maxAgeInDays} days of inactivity:

📍 <b>Wallet:</b> <code>${userWallet.address}</code>
🏷️ <b>Name:</b> ${userWallet.name}
📅 <b>Added:</b> ${new Date(userWallet.createdAt).toLocaleDateString()}
🗑️ <b>Removed:</b> ${new Date().toLocaleDateString()}

<i>This is an automatic cleanup to optimize system performance. You can re-add the wallet anytime using /add command.</i>

💡 <b>Tip:</b> Wallets are automatically removed after ${this.config.maxAgeInDays} days to keep the system running efficiently.
    `

    try {
      await bot.sendMessage(userWallet.userId, message, { parse_mode: 'HTML' })
    } catch (error) {
      console.error(`Failed to send removal notification to user ${userWallet.userId}:`, error)
    }
  }

  /**
   * Notify user of upcoming wallet expiration
   */
  private async notifyUserOfExpiration(userWallet: any): Promise<void> {
    if (!this.config.enableNotifications) return

    const message = `
⚠️ <b>Wallet Expiring Soon</b>

Your wallet will be automatically removed in 1 day:

📍 <b>Wallet:</b> <code>${userWallet.address}</code>
🏷️ <b>Name:</b> ${userWallet.name}
📅 <b>Added:</b> ${new Date(userWallet.createdAt).toLocaleDateString()}
⏰ <b>Expires:</b> Tomorrow

<b>What you can do:</b>
• Keep the wallet: No action needed if you want to continue tracking
• Remove manually: Use /delete command
• Re-add later: You can always add it back with /add

💡 <b>Note:</b> Wallets are automatically cleaned up after ${this.config.maxAgeInDays} days to optimize system performance.
    `

    try {
      await bot.sendMessage(userWallet.userId, message, { parse_mode: 'HTML' })
    } catch (error) {
      console.error(`Failed to send expiration warning to user ${userWallet.userId}:`, error)
    }
  }

  /**
   * Get cleanup statistics
   */
  public async getCleanupStats(): Promise<{
    totalWallets: number
    walletsExpiringSoon: number
    walletsExpiredToday: number
  }> {
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - this.config.maxAgeInDays)

    const expiringSoonDate = new Date()
    expiringSoonDate.setDate(expiringSoonDate.getDate() - (this.config.maxAgeInDays - 1))

    try {
      const totalWallets = await this.prismaWalletRepository.getTotalWalletCount()
      const walletsExpiringSoon = await this.prismaWalletRepository.getWalletsOlderThan(expiringSoonDate, 0, 1000)
      const walletsExpiredToday = await this.prismaWalletRepository.getWalletsOlderThan(cutoffDate, 0, 1000)

      return {
        totalWallets,
        walletsExpiringSoon: walletsExpiringSoon.length,
        walletsExpiredToday: walletsExpiredToday.length
      }
    } catch (error) {
      console.error('Failed to get cleanup stats:', error)
      return {
        totalWallets: 0,
        walletsExpiringSoon: 0,
        walletsExpiredToday: 0
      }
    }
  }

  /**
   * Update cleanup configuration
   */
  public updateConfig(newConfig: Partial<WalletCleanupConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log(chalk.blueBright('🔧 Wallet cleanup configuration updated:'))
    console.log(chalk.cyanBright(JSON.stringify(this.config, null, 2)))
  }
}
