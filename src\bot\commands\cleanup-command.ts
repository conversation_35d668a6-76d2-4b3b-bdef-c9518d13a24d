import TelegramBot from 'node-telegram-bot-api'
import { CronJobs } from '../../lib/cron-jobs'
import chalk from 'chalk'

export class CleanupCommand {
  private bot: TelegramBot
  private cronJobs: CronJobs

  constructor(bot: TelegramBot) {
    this.bot = bot
    this.cronJobs = new CronJobs()
  }

  public cleanupCommandHandler(): void {
    // Admin command to manage wallet cleanup
    this.bot.onText(/\/cleanup_stats/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      // Check if user is admin
      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        const cleanupService = this.cronJobs.getWalletCleanupService()
        const stats = await cleanupService.getCleanupStats()

        const statsMessage = `
🧹 <b>Wallet Cleanup Statistics</b>

📊 <b>Current Status:</b>
• Total Wallets: ${stats.totalWallets}
• Wallets Expiring Soon: ${stats.walletsExpiringSoon}
• Wallets Expired Today: ${stats.walletsExpiredToday}

⚙️ <b>Configuration:</b>
• Auto-cleanup: Every 5 days
• Notifications: Enabled
• Warning: 1 day before removal

🕐 <b>Schedule:</b>
• Cleanup runs daily at 2:00 AM
• Warnings sent daily at 1:00 AM

💡 <b>Available Commands:</b>
/cleanup_stats - Show these statistics
/cleanup_run - Manually run cleanup
/cleanup_dry - Run cleanup in dry-run mode
        `

        await this.bot.sendMessage(message.chat.id, statsMessage, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error getting cleanup stats:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving cleanup statistics.')
      }
    })

    // Manual cleanup command
    this.bot.onText(/\/cleanup_run/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        this.bot.sendMessage(message.chat.id, '🧹 Starting manual wallet cleanup...')

        const result = await this.cronJobs.manualWalletCleanup()

        const resultMessage = `
✅ <b>Manual Cleanup Completed</b>

📊 <b>Results:</b>
• Wallets Checked: ${result.totalWalletsChecked}
• Wallets Removed: ${result.walletsRemoved}
• Users Notified: ${result.usersNotified}
• Errors: ${result.errors.length}

${result.errors.length > 0 ? `\n❌ <b>Errors:</b>\n${result.errors.slice(0, 5).join('\n')}` : ''}
        `

        await this.bot.sendMessage(message.chat.id, resultMessage, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error running manual cleanup:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error running manual cleanup.')
      }
    })

    // Dry run cleanup command
    this.bot.onText(/\/cleanup_dry/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        this.bot.sendMessage(message.chat.id, '🔍 Running cleanup in dry-run mode...')

        const cleanupService = this.cronJobs.getWalletCleanupService()

        // Temporarily enable dry run
        cleanupService.updateConfig({ dryRun: true })
        const result = await cleanupService.cleanupOldWallets()
        cleanupService.updateConfig({ dryRun: false })

        const resultMessage = `
🔍 <b>Dry Run Cleanup Results</b>

📊 <b>Would be removed:</b>
• Wallets Checked: ${result.totalWalletsChecked}
• Wallets to Remove: ${result.walletsRemoved}
• Users to Notify: ${result.usersNotified}
• Errors: ${result.errors.length}

💡 <b>Note:</b> This was a simulation. No wallets were actually removed.

${result.errors.length > 0 ? `\n❌ <b>Potential Errors:</b>\n${result.errors.slice(0, 5).join('\n')}` : ''}
        `

        await this.bot.sendMessage(message.chat.id, resultMessage, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error running dry cleanup:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error running dry cleanup.')
      }
    })

    console.log(chalk.greenBright('🧹 Cleanup admin commands registered'))
  }

  // Button handler for inline keyboard
  public async handleCleanupStatsButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    const adminChatId = process.env.ADMIN_CHAT_ID

    // Check if user is admin
    if (userId !== adminChatId) {
      this.bot.sendMessage(message.chat.id, '❌ This feature is only available to administrators.')
      return
    }

    try {
      const stats = await this.walletCleanupService.getCleanupStats()

      const statsMessage = `
🧹 <b>Wallet Cleanup Statistics - Enhanced Edition</b>

📊 <b>Current Status:</b>
• Total wallets tracked: ${stats.totalWallets}
• Active wallets: ${stats.activeWallets}
• Inactive wallets: ${stats.inactiveWallets}
• Wallets pending cleanup: ${stats.pendingCleanup}

🗑️ <b>Cleanup History:</b>
• Total cleaned up: ${stats.totalCleaned}
• Last cleanup: ${stats.lastCleanup ? stats.lastCleanup.toLocaleString() : 'Never'}
• Next cleanup: ${stats.nextCleanup ? stats.nextCleanup.toLocaleString() : 'Scheduled'}

⚙️ <b>Settings:</b>
• Cleanup after: ${stats.cleanupAfterDays} days of inactivity
• Warning period: 24 hours before cleanup
• Auto cleanup: ${stats.autoCleanupEnabled ? 'Enabled' : 'Disabled'}

💡 <b>Enhanced Features:</b>
✅ Automatic cleanup after 5 days
✅ 24-hour warning notifications
✅ Smart activity detection
✅ Bulk cleanup operations

<i>Keeping your wallet tracker optimized! 🚀</i>
      `

      await this.bot.sendMessage(message.chat.id, statsMessage, { parse_mode: 'HTML' })

    } catch (error) {
      console.error('Error getting cleanup stats:', error)
      this.bot.sendMessage(message.chat.id, '❌ Error retrieving cleanup statistics.')
    }
  }
}
