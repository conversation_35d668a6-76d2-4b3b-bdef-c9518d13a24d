{"cSpell.userWords": [], "cSpell.enabled": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.defaultFormatter": "vscode.typescript-language-features", "editor.formatOnSave": true, "eslint.format.enable": true, "editor.tabSize": 2, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "css.validate": false, "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 120, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "cSpell.words": ["mantine"], "vsicons.presets.nestjs": true, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}