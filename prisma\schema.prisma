generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// SQLite doesn't support enums, using String with default values instead

model User {
  id                      String             @id
  username                String
  firstName               String
  lastName                String

  hasDonated              <PERSON>ole<PERSON>          @default(false)

  botStatus               String           @default("ACTIVE")

  personalWalletPubKey    String
  personalWalletPrivKey   String

  userSubscription        UserSubscription?
  userWallets             UserWallet[]
  userPromotions          UserPromotion[]
  groups                  Group[]

  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
}

model Wallet {
  id            String         @id @default(cuid())
  address       String

  userWallets   UserWallet[]
}

model UserWallet {
  userId          String
  walletId        String
  name            String
  address         String

  handiCatStatus  String           @default("ACTIVE")
  status          String           @default("ACTIVE")

  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  user            User             @relation(fields: [userId], references: [id])
  wallet          Wallet           @relation(fields: [walletId], references: [id])

  @@id([userId, walletId])
}

model UserSubscription {
  id                             String            @id @default(cuid())
  plan                           String            @default("FREE")

  isCanceled                     Boolean           @default(false)
  subscriptionCurrentPeriodEnd   DateTime?

  createdAt                      DateTime          @default(now())
  updatedAt                      DateTime          @updatedAt

  userId                         String            @unique
  user                           User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Promotion {
  id String @id @default(cuid())
  name String
  type String
  price Float
  isActive Boolean @default(true)
  isStackable Boolean

  userPromotions UserPromotion[]
}

model UserPromotion {
   id String @id @default(cuid())

   purchasedAt DateTime  @default(now())

   userId String
   user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  promotionId String
  promotion    Promotion    @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Group {
  id String @id

  name String

  userId String
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
