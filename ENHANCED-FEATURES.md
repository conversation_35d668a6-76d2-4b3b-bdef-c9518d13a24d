# 🚀 Enhanced Wallet Tracker - Unlimited Tracking with Multiple Helius API Keys

This document describes the enhanced features implemented for unlimited Solana wallet tracking using multiple Helius API keys.

## 🎯 Key Enhancements

### ✅ **Unlimited Wallet Tracking**
- Removed all subscription-based wallet limits
- Track as many wallets as your API keys can handle
- No more 10/50/100/220 wallet restrictions

### ✅ **Multiple Helius API Key Support**
- Load balancing across multiple Helius API keys
- Automatic failover when keys hit rate limits
- Round-robin and random distribution strategies
- Auto-recovery of failed keys after cooldown period

### ✅ **Enhanced Connection Management**
- Intelligent connection pooling
- Separate connections for different wallets
- Improved error handling and retry logic
- Real-time connection health monitoring

### ✅ **Standalone Tracking Service**
- Independent wallet tracker (no Telegram dependency)
- Event-driven architecture
- Programmatic API for custom integrations
- Real-time transaction notifications

## 🔧 Configuration

### Environment Variables

Update your `.env` file with multiple Helius API keys:

```env
# Multiple Helius API keys (comma-separated)
HELIUS_API_KEYS=key1,key2,key3,key4,key5

# Multiple RPC endpoints for load balancing
RPC_ENDPOINTS=https://rpc1.com,https://rpc2.com,https://rpc3.com

# Telegram Bot Token (for bot usage)
BOT_TOKEN=your_telegram_bot_token

# Database connection
DATABASE_URL=your_postgresql_connection_string
```

### Backward Compatibility

The system maintains backward compatibility with single API key setups:

```env
# Legacy single key (still supported)
HELIUS_API_KEY=your_single_helius_key
```

## 🚀 Usage Examples

### 1. Using the Enhanced Telegram Bot

The original Telegram bot now supports unlimited wallets automatically:

```bash
# Install dependencies
pnpm install

# Set up environment
cp .env.example .env
# Edit .env with your multiple Helius API keys

# Run database migrations
pnpm db:migrate

# Start the bot
pnpm start
```

The bot will automatically:
- Load balance across your Helius API keys
- Allow unlimited wallet additions
- Provide failover protection
- Show enhanced connection statistics

### 2. Using the Standalone Tracker

```typescript
import { StandaloneWalletTracker } from './src/services/standalone-wallet-tracker'

const tracker = new StandaloneWalletTracker()

// Listen for transactions
tracker.on('transaction', (event) => {
  console.log('New transaction:', event.description)
  console.log('Wallet:', event.walletAddress)
  console.log('Type:', event.transactionType)
  console.log('Amount:', event.amount)
})

// Add unlimited wallets
await tracker.addWallet({
  address: 'wallet_address_1',
  name: 'Whale Wallet'
})

await tracker.addWallet({
  address: 'wallet_address_2', 
  name: 'DeFi Trader'
})

// Add as many as you want...

// Start tracking
await tracker.start()
```

### 3. Custom Integration Example

```typescript
import { StandaloneWalletTracker } from './src/services/standalone-wallet-tracker'

const tracker = new StandaloneWalletTracker()

// Filter for high-value transactions
tracker.on('transaction', (event) => {
  if (event.priceUsd && event.priceUsd > 10000) {
    // Send alert to your system
    sendHighValueAlert(event)
  }
  
  // Track specific tokens
  if (event.tokenAddress === 'USDC_ADDRESS') {
    logUSDCTransaction(event)
  }
})

// Add wallets from your database
const wallets = await getWalletsFromDatabase()
for (const wallet of wallets) {
  await tracker.addWallet(wallet)
}

await tracker.start()
```

## 📊 Connection Statistics

The enhanced system provides real-time statistics:

```typescript
import { RpcConnectionManager } from './src/providers/solana'

const stats = RpcConnectionManager.getConnectionStats()
console.log('Total RPC Endpoints:', stats.totalRpcEndpoints)
console.log('Total Helius Keys:', stats.totalHeliusKeys)
console.log('Available Helius Keys:', stats.availableHeliusKeys)
console.log('Failed Helius Keys:', stats.failedHeliusKeys)
```

## 🛡️ Failover & Recovery

### Automatic Failover
- When a Helius API key hits rate limits, it's automatically marked as failed
- System switches to next available key
- Failed keys are auto-recovered after 5 minutes

### Load Balancing Strategies
- **Round-robin**: Distributes wallets evenly across API keys
- **Random**: Randomly selects API keys for new connections
- **Failover**: Automatically switches to healthy keys

### Error Handling
- Retry logic with exponential backoff
- Graceful degradation when keys are unavailable
- Fallback to regular RPC endpoints when needed

## 🔍 Monitoring & Debugging

### Enhanced Logging
The system provides detailed logging for:
- Connection establishment
- API key usage and rotation
- Failover events
- Transaction processing
- Error conditions

### Health Checks
```typescript
// Check system health
const tracker = new StandaloneWalletTracker()
const stats = tracker.getStats()

console.log('System Health:', {
  totalWallets: stats.totalWallets,
  activeWallets: stats.activeWallets,
  availableKeys: stats.availableHeliusKeys,
  isRunning: stats.isRunning
})
```

## 🎛️ Advanced Configuration

### Rate Limiting
Customize rate limiting per wallet:

```typescript
// In src/constants/handi-cat.ts
export const MAX_TPS_ALLOWED = 2.0  // Transactions per second
export const MAX_TPS_FOR_BAN = 2.2  // Ban threshold
```

### Connection Timeouts
Adjust connection timeouts in the RPC manager:

```typescript
// Custom connection with timeout
const connection = new Connection(rpcUrl, {
  commitment: 'processed',
  wsEndpoint: wsUrl,
  httpHeaders: { 'timeout': '30000' }
})
```

### Memory Management
For tracking thousands of wallets:

```typescript
// Implement cleanup for inactive wallets
setInterval(() => {
  tracker.cleanupInactiveWallets()
}, 60000) // Every minute
```

## 🚨 Best Practices

### 1. API Key Management
- Use at least 3-5 Helius API keys for redundancy
- Monitor your API key usage through Helius dashboard
- Rotate keys periodically for security

### 2. Resource Management
- Monitor memory usage when tracking many wallets
- Implement cleanup for inactive wallets
- Use appropriate rate limiting

### 3. Error Handling
- Always handle transaction events with try-catch
- Implement proper logging for debugging
- Set up monitoring alerts for system health

### 4. Scaling Considerations
- For 1000+ wallets, consider multiple instances
- Use database clustering for high throughput
- Implement proper load balancing

## 🔧 Troubleshooting

### Common Issues

**Issue**: "No Helius API keys configured"
**Solution**: Check your `.env` file has `HELIUS_API_KEYS` set

**Issue**: "All Helius keys failed"
**Solution**: Check API key validity and rate limits

**Issue**: "High memory usage"
**Solution**: Implement wallet cleanup and reduce tracking frequency

**Issue**: "Connection timeouts"
**Solution**: Add more RPC endpoints or increase timeout values

### Debug Mode
Enable debug logging:

```env
NODE_ENV=development
DEBUG=wallet-tracker:*
```

## 📈 Performance Metrics

Expected performance with multiple API keys:

- **Single API Key**: ~100-200 wallets
- **3 API Keys**: ~300-600 wallets  
- **5 API Keys**: ~500-1000 wallets
- **10+ API Keys**: 1000+ wallets

*Performance depends on transaction frequency and API key limits*

## 🤝 Contributing

To contribute to the enhanced features:

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This enhanced version maintains the same license as the original project.

## 🆘 Support

For issues with the enhanced features:

1. Check this documentation
2. Review the example files
3. Check existing GitHub issues
4. Create a new issue with detailed information

---

**🎉 Enjoy unlimited Solana wallet tracking with multiple Helius API keys!**
