#!/bin/bash

# Handi Cat Wallet Tracker Enhanced Edition - Setup Script
# =========================================================

echo "🐱 Handi Cat Wallet Tracker Enhanced Edition Setup"
echo "=================================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# Check if Node.js is installed
print_step "Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_status "Node.js found: $NODE_VERSION"
    
    # Check if version is 14.x or higher
    NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR_VERSION" -lt 14 ]; then
        print_error "Node.js version 14.x or higher required. Current: $NODE_VERSION"
        echo "Please update Node.js: https://nodejs.org/"
        exit 1
    fi
else
    print_error "Node.js not found. Please install Node.js 14.x or higher"
    echo "Download from: https://nodejs.org/"
    exit 1
fi

# Check if pnpm is installed
print_step "Checking pnpm installation..."
if command -v pnpm &> /dev/null; then
    PNPM_VERSION=$(pnpm --version)
    print_status "pnpm found: $PNPM_VERSION"
else
    print_warning "pnpm not found. Installing pnpm..."
    npm install -g pnpm
    if [ $? -eq 0 ]; then
        print_status "pnpm installed successfully"
    else
        print_error "Failed to install pnpm"
        exit 1
    fi
fi

# Install dependencies
print_step "Installing dependencies..."
pnpm install
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Setup environment file
print_step "Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.example .env
    print_status "Environment file created (.env)"
    print_warning "Please edit .env file with your configuration:"
    echo ""
    echo -e "${CYAN}Required variables:${NC}"
    echo "  HELIUS_API_KEYS=key1,key2,key3,key4,key5"
    echo "  BOT_TOKEN=your_telegram_bot_token"
    echo "  ADMIN_CHAT_ID=your_telegram_chat_id"
    echo "  DATABASE_URL=your_database_connection_string"
    echo ""
else
    print_info "Environment file already exists (.env)"
fi

# Check if database is configured
print_step "Checking database configuration..."
if grep -q "your_database_connection_string" .env 2>/dev/null; then
    print_warning "Database URL not configured in .env"
    echo "Please set DATABASE_URL in .env file"
else
    print_info "Database configuration found"
fi

# Setup database (if configured)
print_step "Setting up database..."
if ! grep -q "your_database_connection_string" .env 2>/dev/null; then
    pnpm db:setup
    if [ $? -eq 0 ]; then
        print_status "Database setup completed"
    else
        print_warning "Database setup failed. Please check your DATABASE_URL"
    fi
else
    print_info "Skipping database setup (not configured)"
fi

# Create logs directory
print_step "Creating logs directory..."
mkdir -p logs
print_status "Logs directory created"

# Display setup summary
echo ""
echo -e "${PURPLE}🎉 Setup Summary${NC}"
echo "================"
echo ""

# Check configuration status
CONFIG_STATUS="❌ Not configured"
if [ -f .env ]; then
    if ! grep -q "your_telegram_bot_token" .env 2>/dev/null; then
        CONFIG_STATUS="✅ Configured"
    fi
fi

echo -e "📦 Dependencies: ${GREEN}✅ Installed${NC}"
echo -e "⚙️  Environment: $CONFIG_STATUS"
echo -e "🗄️  Database: $([ -f .env ] && ! grep -q "your_database_connection_string" .env 2>/dev/null && echo "✅ Configured" || echo "❌ Not configured")"
echo -e "📁 Logs: ${GREEN}✅ Ready${NC}"
echo ""

# Next steps
echo -e "${CYAN}📋 Next Steps:${NC}"
echo ""
echo "1. 🔑 Get Helius API Keys:"
echo "   - Visit: https://helius.xyz"
echo "   - Get 3-5 API keys for best performance"
echo ""
echo "2. 🤖 Create Telegram Bot:"
echo "   - Message @BotFather on Telegram"
echo "   - Use /newbot command"
echo "   - Save your bot token"
echo ""
echo "3. ⚙️  Configure .env file:"
echo "   - Edit .env with your API keys and bot token"
echo "   - Set your database connection string"
echo ""
echo "4. 🚀 Start the bot:"
echo "   pnpm start"
echo ""

# BotFather commands
echo -e "${YELLOW}🤖 BotFather Commands Setup:${NC}"
echo ""
echo "Send these commands to @BotFather:"
echo ""
echo "/setcommands"
echo "Then paste:"
echo ""
cat << 'EOF'
start - 🏠 Open main menu
help - 🆘 Show all available commands
add - ➕ Add a single wallet to track
delete - ➖ Remove a single wallet
bulk_add - 📦 Add multiple wallets at once (up to 100)
bulk_remove - 🗑️ Remove multiple wallets at once
list_wallets - 📊 View your tracked wallets
manage - ⚙️ Manage all your tracked wallets
health - 🏥 System health status (admin only)
errors - 🚨 Error summary (admin only)
logs - 📄 View recent logs (admin only)
monitoring_config - ⚙️ View logging config (admin only)
cleanup_stats - 🧹 Cleanup statistics (admin only)
ban_wallet - 🚫 Ban a wallet (admin only)
upgrade - 💎 Upgrade subscription (optional)
help_notify - 📱 How notifications work
help_group - 👥 Add bot to groups
EOF

echo ""
echo -e "${GREEN}🎯 Setup completed! Your enhanced wallet tracker is ready.${NC}"
echo ""
echo "📚 Documentation:"
echo "  - README.md: Complete guide"
echo "  - QUICK-START-GUIDE.md: 5-minute setup"
echo "  - BULK-WALLET-GUIDE.md: Bulk operations"
echo ""
echo "🚀 Start tracking unlimited wallets with bulk operations!"
