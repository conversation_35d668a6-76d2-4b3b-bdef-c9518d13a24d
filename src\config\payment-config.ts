import dotenv from 'dotenv'
import chalk from 'chalk'

dotenv.config()

export interface PaymentConfig {
  enabled: boolean
  walletAddress: string | null
  allowDonations: boolean
  allowSubscriptions: boolean
  allowPromotions: boolean
}

/**
 * Payment Configuration Manager
 * 
 * Handles whether payment features are enabled or disabled
 * based on environment variables.
 */
export class PaymentConfigManager {
  private static config: PaymentConfig

  static initialize(): PaymentConfig {
    const disablePayments = process.env.DISABLE_PAYMENTS?.toLowerCase() === 'true'
    const walletAddress = process.env.HANDICAT_WALLET_ADDRESS?.trim()
    
    // Determine if payments should be enabled
    const paymentsEnabled = !disablePayments && !!walletAddress

    this.config = {
      enabled: paymentsEnabled,
      walletAddress: walletAddress || null,
      allowDonations: paymentsEnabled,
      allowSubscriptions: paymentsEnabled,
      allowPromotions: paymentsEnabled
    }

    // Log configuration
    if (paymentsEnabled) {
      console.log(chalk.greenBright('💰 Payment features ENABLED'))
      console.log(chalk.cyanBright(`Payment wallet: ${walletAddress}`))
    } else {
      console.log(chalk.yellowBright('🆓 Payment features DISABLED'))
      console.log(chalk.blueBright('Bot running in free-only mode'))
    }

    return this.config
  }

  static getConfig(): PaymentConfig {
    if (!this.config) {
      return this.initialize()
    }
    return this.config
  }

  static isPaymentEnabled(): boolean {
    return this.getConfig().enabled
  }

  static getWalletAddress(): string | null {
    return this.getConfig().walletAddress
  }

  static canAcceptDonations(): boolean {
    return this.getConfig().allowDonations
  }

  static canProcessSubscriptions(): boolean {
    return this.getConfig().allowSubscriptions
  }

  static canProcessPromotions(): boolean {
    return this.getConfig().allowPromotions
  }

  /**
   * Get user-friendly message about payment status
   */
  static getPaymentStatusMessage(): string {
    const config = this.getConfig()
    
    if (config.enabled) {
      return `
💰 <b>Payment Features Available</b>

✅ Subscriptions enabled
✅ Donations enabled  
✅ Promotions enabled

💡 <b>Note:</b> With unlimited wallet tracking, subscriptions are optional!
      `
    } else {
      return `
🆓 <b>Free Mode Active</b>

✅ Unlimited wallet tracking
✅ All features available
✅ No payment required

💡 <b>Note:</b> This bot runs completely free with unlimited wallets!
      `
    }
  }

  /**
   * Update configuration at runtime
   */
  static updateConfig(newConfig: Partial<PaymentConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log(chalk.blueBright('🔧 Payment configuration updated:'))
    console.log(chalk.cyanBright(JSON.stringify(this.config, null, 2)))
  }
}

// Initialize on import
PaymentConfigManager.initialize()
