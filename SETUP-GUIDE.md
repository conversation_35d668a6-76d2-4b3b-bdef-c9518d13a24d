# 🚀 Complete Setup Guide - Unlimited Wallet Tracking with Multiple Helius API Keys

This guide will help you set up the enhanced wallet tracker with unlimited wallet support and automatic cleanup.

## 📋 Prerequisites

- Node.js 14.x or higher
- PostgreSQL database
- Multiple Helius API keys (recommended: 3-5 keys)
- Telegram <PERSON><PERSON>

## 🔧 Step-by-Step Setup

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
git clone https://github.com/DracoR22/handi-cat_wallet-tracker.git
cd handi-cat_wallet-tracker
pnpm install
```

### 2. Get Multiple Helius API Keys

1. Go to [https://www.helius.dev](https://www.helius.dev)
2. Create an account and get your first API key
3. **IMPORTANT**: Create 3-5 separate API keys for load balancing
4. Each key should have sufficient rate limits for your needs

### 3. Configure Environment Variables

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```env
# ============================================================================
# HELIUS API KEYS CONFIGURATION (REQUIRED FOR UNLIMITED WALLET TRACKING)
# ============================================================================
# Add your multiple Helius API keys here, separated by commas (NO SPACES)
# Example with 5 keys:
HELIUS_API_KEYS=abc123-def456-ghi789,xyz987-uvw654-rst321,mno147-pqr258-stu369,jkl741-ghi852-def963,vwx159-yza753-bcd486

# Multiple RPC endpoints for load balancing
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana

# Telegram Bot Token from BotFather
BOT_TOKEN=your_telegram_bot_token_here

# Your Telegram chat ID for admin commands
ADMIN_CHAT_ID=your_telegram_chat_id

# PostgreSQL database connection
DATABASE_URL=postgresql://username:password@localhost:5432/wallet_tracker

# Your wallet address for donations
HANDICAT_WALLET_ADDRESS=your-solana-wallet-address

# Environment
NODE_ENV=development
```

### 4. Set Up Database

Run the database migrations:
```bash
pnpm db:migrate
```

This will:
- Create all necessary tables
- Add the new `createdAt` and `updatedAt` fields for wallet tracking
- Set up the database schema for unlimited wallet tracking

### 5. Configure Telegram Bot

1. Create a bot with [@BotFather](https://t.me/botfather)
2. Get your bot token and add it to `.env`
3. Get your Telegram chat ID:
   - Send a message to your bot
   - Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Find your chat ID in the response

### 6. Start the Application

For development (with polling):
```bash
# Edit src/providers/telegram.ts and uncomment line 13
# Comment out the webhook setup lines
pnpm start
```

For production (with webhooks):
```bash
# Make sure webhook is configured in src/providers/telegram.ts
# Set APP_URL in .env
pnpm start
```

## 🎯 Key Features Enabled

### ✅ Unlimited Wallet Tracking
- No more 10/50/100/220 wallet limits
- Track as many wallets as your API keys can handle

### ✅ Multiple Helius API Key Load Balancing
- Automatic distribution across your API keys
- Failover when keys hit rate limits
- Auto-recovery after cooldown periods

### ✅ Automatic Wallet Cleanup (5 Days)
- Wallets automatically removed after 5 days
- Users get warning notifications 1 day before removal
- Cleanup runs daily at 2:00 AM
- Warning notifications sent at 1:00 AM

### ✅ Enhanced Admin Commands
- `/cleanup_stats` - View cleanup statistics
- `/cleanup_run` - Manually run cleanup
- `/cleanup_dry` - Test cleanup without removing wallets

## 📊 Monitoring and Management

### Check System Status

The bot will display enhanced statistics on startup:
```
🚀 ENHANCED WALLET TRACKER INITIALIZED
📡 RPC Endpoints: 3
🔑 Helius API Keys: 5/5 available
💰 Wallet Limits: UNLIMITED (Enhanced for multiple API keys)
⚡ Load Balancing: ENABLED
🛡️ Failover Protection: ENABLED
```

### Admin Commands

As an admin, you can use these commands:

1. **Check Cleanup Statistics**:
   ```
   /cleanup_stats
   ```
   Shows total wallets, expiring wallets, and system configuration.

2. **Manual Cleanup**:
   ```
   /cleanup_run
   ```
   Manually trigger wallet cleanup (removes wallets older than 5 days).

3. **Dry Run Cleanup**:
   ```
   /cleanup_dry
   ```
   Test what would be removed without actually removing anything.

### User Experience

Users will receive notifications:

1. **24 hours before removal**:
   ```
   ⚠️ Wallet Expiring Soon
   Your wallet will be automatically removed in 1 day:
   📍 Wallet: [address]
   🏷️ Name: [name]
   ```

2. **When wallet is removed**:
   ```
   🧹 Wallet Automatically Removed
   Your wallet has been automatically removed after 5 days:
   📍 Wallet: [address]
   🏷️ Name: [name]
   ```

## 🔧 Customization

### Change Cleanup Period

Edit `src/lib/cron-jobs.ts`:
```typescript
this.walletCleanupService = new WalletCleanupService({
  maxAgeInDays: 7, // Change from 5 to 7 days
  enableNotifications: true,
  dryRun: false,
  batchSize: 50
})
```

### Disable Automatic Cleanup

In `src/main.ts`, comment out:
```typescript
// await this.cronJobs.startWalletCleanup()
```

### Change Cleanup Schedule

Edit `src/services/wallet-cleanup-service.ts`:
```typescript
// Change from daily at 2 AM to weekly
cron.schedule('0 2 * * 0', async () => { // Weekly on Sunday
```

## 🚨 Troubleshooting

### Common Issues

1. **"No Helius API keys configured"**
   - Check your `.env` file has `HELIUS_API_KEYS` set
   - Make sure there are no spaces between keys
   - Verify keys are valid

2. **"Database connection failed"**
   - Check your `DATABASE_URL` is correct
   - Ensure PostgreSQL is running
   - Run `pnpm db:migrate` to set up tables

3. **"Bot not responding"**
   - Verify `BOT_TOKEN` is correct
   - Check if using polling vs webhook correctly
   - Ensure bot is not already running elsewhere

4. **"High memory usage"**
   - Monitor the number of tracked wallets
   - Check cleanup is running properly
   - Consider reducing batch sizes

### Debug Mode

Enable detailed logging:
```env
NODE_ENV=development
DEBUG=wallet-tracker:*
```

### Performance Monitoring

Check connection statistics:
```typescript
import { RpcConnectionManager } from './src/providers/solana'
const stats = RpcConnectionManager.getConnectionStats()
console.log(stats)
```

## 📈 Scaling Recommendations

### For 100-500 Wallets
- Use 3-5 Helius API keys
- Default configuration should work fine

### For 500-1000 Wallets
- Use 5-10 Helius API keys
- Add more RPC endpoints
- Monitor memory usage

### For 1000+ Wallets
- Use 10+ Helius API keys
- Consider multiple instances
- Implement database clustering
- Monitor system resources closely

## 🆘 Support

If you encounter issues:

1. Check this setup guide
2. Review the `ENHANCED-FEATURES.md` documentation
3. Check existing GitHub issues
4. Create a new issue with:
   - Your configuration (without API keys)
   - Error messages
   - Steps to reproduce

## 🎉 You're Ready!

Your enhanced wallet tracker is now configured with:
- ✅ Unlimited wallet tracking
- ✅ Multiple Helius API key load balancing
- ✅ Automatic 5-day wallet cleanup
- ✅ Enhanced monitoring and admin tools

Start tracking unlimited Solana wallets! 🚀
