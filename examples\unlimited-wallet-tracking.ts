/**
 * Example: Unlimited Wallet Tracking with Multiple Helius API Keys
 * 
 * This example demonstrates how to use the enhanced wallet tracker
 * to monitor unlimited Solana wallets using multiple Helius API keys.
 */

import { StandaloneWalletTracker, WalletTransactionEvent } from '../src/services/standalone-wallet-tracker'
import { RpcConnectionManager } from '../src/providers/solana'
import chalk from 'chalk'
import dotenv from 'dotenv'

dotenv.config()

async function main() {
  console.log(chalk.blueBright('🚀 Starting Unlimited Wallet Tracking Example'))
  
  // Create the standalone tracker
  const tracker = new StandaloneWalletTracker()

  // Set up event listeners
  tracker.on('transaction', (event: WalletTransactionEvent) => {
    console.log(chalk.greenBright('\n📊 New Transaction Detected:'))
    console.log(chalk.yellowBright(`Wallet: ${event.walletAddress}`))
    console.log(chalk.cyanBright(`Type: ${event.transactionType.toUpperCase()}`))
    console.log(chalk.whiteBright(`Description: ${event.description}`))
    console.log(chalk.magentaBright(`Signature: ${event.transactionSignature}`))
    
    if (event.tokenAddress) {
      console.log(chalk.greenBright(`Token: ${event.tokenAddress}`))
    }
    
    if (event.amount) {
      console.log(chalk.yellowBright(`Amount: ${event.amount}`))
    }
    
    if (event.priceUsd) {
      console.log(chalk.cyanBright(`Price USD: $${event.priceUsd}`))
    }
    
    if (event.marketCap) {
      console.log(chalk.magentaBright(`Market Cap: $${event.marketCap}`))
    }
    
    console.log(chalk.gray(`Timestamp: ${new Date(event.timestamp).toISOString()}`))
    console.log(chalk.gray('─'.repeat(80)))
  })

  tracker.on('error', (error) => {
    console.error(chalk.redBright('❌ Tracker Error:'), error)
  })

  // Example wallet addresses (replace with real addresses you want to track)
  const walletsToTrack = [
    {
      address: '5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1', // Example wallet 1
      name: 'Whale Wallet 1'
    },
    {
      address: '7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi', // Example wallet 2  
      name: 'DeFi Trader'
    },
    {
      address: '2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S', // Example wallet 3
      name: 'NFT Collector'
    }
    // Add as many wallets as you want - no limits!
  ]

  try {
    // Add wallets to track
    console.log(chalk.blueBright('\n📝 Adding wallets to track...'))
    for (const wallet of walletsToTrack) {
      await tracker.addWallet(wallet)
    }

    // Display connection statistics
    const stats = tracker.getStats()
    console.log(chalk.cyanBright('\n📊 Connection Statistics:'))
    console.log(`Total RPC Endpoints: ${stats.totalRpcEndpoints}`)
    console.log(`Total Helius Keys: ${stats.totalHeliusKeys}`)
    console.log(`Available Helius Keys: ${stats.availableHeliusKeys}`)
    console.log(`Total Wallets: ${stats.totalWallets}`)
    console.log(`Enabled Wallets: ${stats.enabledWallets}`)

    // Start tracking
    console.log(chalk.greenBright('\n🎯 Starting wallet tracking...'))
    await tracker.start()

    console.log(chalk.greenBright('\n✅ Tracker is now running! Press Ctrl+C to stop.'))
    
    // Keep the process running
    process.on('SIGINT', async () => {
      console.log(chalk.yellowBright('\n🛑 Stopping tracker...'))
      await tracker.stop()
      process.exit(0)
    })

    // Optional: Add more wallets dynamically after 30 seconds
    setTimeout(async () => {
      console.log(chalk.blueBright('\n➕ Adding additional wallet dynamically...'))
      await tracker.addWallet({
        address: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM', // Another example
        name: 'Dynamic Wallet'
      })
    }, 30000)

    // Optional: Show stats every 60 seconds
    setInterval(() => {
      const currentStats = tracker.getStats()
      console.log(chalk.cyanBright('\n📈 Current Stats:'))
      console.log(`Active Wallets: ${currentStats.activeWallets}`)
      console.log(`Available Helius Keys: ${currentStats.availableHeliusKeys}/${currentStats.totalHeliusKeys}`)
    }, 60000)

  } catch (error) {
    console.error(chalk.redBright('❌ Error:'), error)
    process.exit(1)
  }
}

// Example of using the original bot with unlimited wallets
async function exampleWithOriginalBot() {
  console.log(chalk.blueBright('🤖 Example: Using Original Bot with Unlimited Wallets'))
  
  // The original bot now supports unlimited wallets automatically
  // Just set up your .env file with multiple Helius API keys:
  
  console.log(chalk.yellowBright('Environment Setup:'))
  console.log('HELIUS_API_KEYS=key1,key2,key3,key4,key5')
  console.log('RPC_ENDPOINTS=https://rpc1.com,https://rpc2.com,https://rpc3.com')
  
  // The bot will automatically:
  // 1. Load balance across multiple Helius API keys
  // 2. Allow unlimited wallet additions (no subscription limits)
  // 3. Provide failover when API keys hit rate limits
  // 4. Distribute wallet monitoring across different connections
  
  console.log(chalk.greenBright('\n✅ Original bot now supports unlimited wallets!'))
}

// Example of monitoring specific token transactions
async function exampleTokenSpecificTracking() {
  const tracker = new StandaloneWalletTracker()
  
  // Filter for specific token transactions
  tracker.on('transaction', (event: WalletTransactionEvent) => {
    // Example: Only track USDC transactions
    const USDC_ADDRESS = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    
    if (event.tokenAddress === USDC_ADDRESS) {
      console.log(chalk.greenBright(`USDC Transaction: ${event.description}`))
    }
    
    // Example: Track high-value transactions
    if (event.priceUsd && event.priceUsd > 10000) {
      console.log(chalk.redBright(`🚨 HIGH VALUE TRANSACTION: $${event.priceUsd}`))
    }
    
    // Example: Track specific transaction types
    if (event.transactionType === 'buy') {
      console.log(chalk.cyanBright(`💰 Buy detected: ${event.description}`))
    }
  })
  
  // Add wallets and start tracking
  await tracker.addWallet({
    address: 'your-wallet-address-here',
    name: 'Token Specific Tracker'
  })
  
  await tracker.start()
}

// Run the main example
if (require.main === module) {
  main().catch(console.error)
}

export {
  main as runUnlimitedTrackingExample,
  exampleWithOriginalBot,
  exampleTokenSpecificTracking
}
