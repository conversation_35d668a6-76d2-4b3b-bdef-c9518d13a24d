import { Connection } from '@solana/web3.js'
import { RpcConnectionManager } from '../providers/solana'
import { logger } from '../services/logger-service'

// Smart transaction processing for free plans
// Processes 2-3 transactions at once with rotation and delays

interface TransactionRequest {
  signature: string
  walletAddress: string
  priority: 'high' | 'medium' | 'low'
  retryCount: number
  lastAttempt?: Date
}

interface ProcessingResult {
  success: boolean
  data?: any
  error?: string
  endpoint?: string
  responseTime: number
}

export class SmartTransactionProcessor {
  private static processingQueue: TransactionRequest[] = []
  private static isProcessing = false
  private static currentBatch: TransactionRequest[] = []

  // Configuration for free plans (from environment variables)
  private static readonly BATCH_SIZE = parseInt(process.env.BATCH_SIZE || '2')
  private static readonly BATCH_SIZE_VARIATION = parseInt(process.env.BATCH_SIZE_VARIATION || '1')
  private static readonly DELAY_BETWEEN_REQUESTS = parseInt(process.env.DELAY_BETWEEN_REQUESTS || '200')
  private static readonly DELAY_BETWEEN_BATCHES = parseInt(process.env.DELAY_BETWEEN_BATCHES || '1000')
  private static readonly MAX_RETRIES = parseInt(process.env.MAX_RETRIES || '3')
  private static readonly RETRY_DELAY = parseInt(process.env.RETRY_DELAY || '2000')

  /**
   * Add transaction to processing queue
   */
  static addTransaction(signature: string, walletAddress: string, priority: 'high' | 'medium' | 'low' = 'medium'): void {
    const request: TransactionRequest = {
      signature,
      walletAddress,
      priority,
      retryCount: 0
    }

    // Add to queue based on priority
    if (priority === 'high') {
      this.processingQueue.unshift(request) // Add to front
    } else {
      this.processingQueue.push(request) // Add to back
    }

    logger.debug(`Transaction added to queue: ${signature.substring(0, 8)}...`, {
      component: 'smart-transaction-processor',
      metadata: {
        queueSize: this.processingQueue.length,
        priority,
        walletAddress: walletAddress.substring(0, 8) + '...'
      }
    })

    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing()
    }
  }

  /**
   * Start the smart processing system
   */
  private static async startProcessing(): Promise<void> {
    if (this.isProcessing) return

    this.isProcessing = true
    logger.info('Smart transaction processor started', {
      component: 'smart-transaction-processor'
    })

    while (this.processingQueue.length > 0) {
      try {
        // Get next batch (configurable size with variation)
        const randomVariation = Math.floor(Math.random() * (this.BATCH_SIZE_VARIATION + 1))
        const batchSize = Math.min(this.BATCH_SIZE + randomVariation, this.processingQueue.length)
        this.currentBatch = this.processingQueue.splice(0, batchSize)

        logger.debug(`Processing batch of ${batchSize} transactions`, {
          component: 'smart-transaction-processor',
          metadata: {
            batchSize,
            remainingInQueue: this.processingQueue.length
          }
        })

        // Process batch with rotation and delays
        await this.processBatch(this.currentBatch)

        // Delay between batches to prevent rate limiting
        if (this.processingQueue.length > 0) {
          logger.debug(`Waiting ${this.DELAY_BETWEEN_BATCHES}ms before next batch`, {
            component: 'smart-transaction-processor'
          })
          await this.delay(this.DELAY_BETWEEN_BATCHES)
        }

      } catch (error) {
        logger.error('Error in batch processing', {
          component: 'smart-transaction-processor',
          error: error instanceof Error ? error.message : String(error)
        })

        // Re-queue failed transactions for retry
        this.currentBatch.forEach(req => {
          if (req.retryCount < this.MAX_RETRIES) {
            req.retryCount++
            req.lastAttempt = new Date()
            this.processingQueue.push(req)
          }
        })
      }
    }

    this.isProcessing = false
    logger.info('Smart transaction processor finished', {
      component: 'smart-transaction-processor'
    })
  }

  /**
   * Process a batch of transactions with smart rotation
   */
  private static async processBatch(batch: TransactionRequest[]): Promise<void> {
    const results: Promise<ProcessingResult>[] = []

    for (let i = 0; i < batch.length; i++) {
      const request = batch[i]

      // Add delay between requests in the same batch
      if (i > 0) {
        await this.delay(this.DELAY_BETWEEN_REQUESTS)
      }

      // Process transaction with rotation
      const resultPromise = this.processTransactionWithRotation(request)
      results.push(resultPromise)
    }

    // Wait for all transactions in batch to complete
    const batchResults = await Promise.allSettled(results)

    // Log batch results
    batchResults.forEach((result, index) => {
      const request = batch[index]
      if (result.status === 'fulfilled') {
        const processingResult = result.value
        if (processingResult.success) {
          logger.info(`Transaction processed successfully: ${request.signature.substring(0, 8)}...`, {
            component: 'smart-transaction-processor',
            metadata: {
              endpoint: processingResult.endpoint,
              responseTime: processingResult.responseTime,
              walletAddress: request.walletAddress.substring(0, 8) + '...'
            }
          })
        } else {
          logger.warn(`Transaction processing failed: ${request.signature.substring(0, 8)}...`, {
            component: 'smart-transaction-processor',
            metadata: {
              error: processingResult.error,
              retryCount: request.retryCount
            }
          })
        }
      } else {
        logger.error(`Transaction processing rejected: ${request.signature.substring(0, 8)}...`, {
          component: 'smart-transaction-processor',
          error: result.reason
        })
      }
    })
  }

  /**
   * Process single transaction with smart endpoint rotation
   */
  private static async processTransactionWithRotation(request: TransactionRequest): Promise<ProcessingResult> {
    const startTime = Date.now()

    // Try regular RPC endpoints first (free and reliable)
    try {
      const connection = RpcConnectionManager.getRandomConnection()
      const result = await this.fetchTransactionWithTimeout(connection, request.signature)

      const responseTime = Date.now() - startTime

      // Track performance
      RpcConnectionManager.trackRpcPerformance(
        connection.rpcEndpoint,
        responseTime,
        true,
        false
      )

      return {
        success: true,
        data: result,
        endpoint: connection.rpcEndpoint,
        responseTime
      }
    } catch (rpcError) {
      logger.debug(`Regular RPC failed for ${request.signature.substring(0, 8)}..., trying Helius`, {
        component: 'smart-transaction-processor',
        error: rpcError instanceof Error ? rpcError.message : String(rpcError)
      })
    }

    // Fallback to Helius with smart key selection
    try {
      const heliusConnection = RpcConnectionManager.getRandomHeliusConnection()
      const result = await this.fetchTransactionWithTimeout(heliusConnection, request.signature)

      const responseTime = Date.now() - startTime

      // Track performance
      RpcConnectionManager.trackRpcPerformance(
        heliusConnection.rpcEndpoint,
        responseTime,
        true,
        true
      )

      return {
        success: true,
        data: result,
        endpoint: heliusConnection.rpcEndpoint,
        responseTime
      }
    } catch (heliusError) {
      const responseTime = Date.now() - startTime

      // Handle API key errors
      const errorMessage = heliusError instanceof Error ? heliusError.message : String(heliusError)
      const bestKey = RpcConnectionManager.getBestHeliusApiKey()
      if (bestKey) {
        RpcConnectionManager.handleApiKeyError(bestKey, 'transaction_fetch', errorMessage)
      }

      return {
        success: false,
        error: errorMessage,
        responseTime
      }
    }
  }

  /**
   * Fetch transaction with timeout
   */
  private static async fetchTransactionWithTimeout(connection: Connection, signature: string, timeoutMs: number = 10000): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Transaction fetch timeout'))
      }, timeoutMs)

      connection.getTransaction(signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0
      }).then(result => {
        clearTimeout(timeout)
        resolve(result)
      }).catch(error => {
        clearTimeout(timeout)
        reject(error)
      })
    })
  }

  /**
   * Utility delay function
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get queue status
   */
  static getQueueStatus(): {
    queueSize: number
    isProcessing: boolean
    currentBatchSize: number
  } {
    return {
      queueSize: this.processingQueue.length,
      isProcessing: this.isProcessing,
      currentBatchSize: this.currentBatch.length
    }
  }

  /**
   * Clear queue (for testing or emergency)
   */
  static clearQueue(): void {
    this.processingQueue = []
    this.currentBatch = []
    logger.info('Transaction processing queue cleared', {
      component: 'smart-transaction-processor'
    })
  }
}
