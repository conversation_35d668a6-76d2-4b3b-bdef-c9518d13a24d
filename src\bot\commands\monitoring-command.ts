import TelegramBot from 'node-telegram-bot-api'
import { monitoring } from '../../services/monitoring-service'
import { logger } from '../../services/logger-service'
import chalk from 'chalk'
import fs from 'fs'
import path from 'path'

export class MonitoringCommand {
  private bot: TelegramBot

  constructor(bot: TelegramBot) {
    this.bot = bot
  }

  public monitoringCommandHandler(): void {
    // System health command
    this.bot.onText(/\/health/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      // Check if user is admin
      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        const healthSummary = monitoring.getHealthSummary()
        await this.bot.sendMessage(message.chat.id, healthSummary, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error getting health summary:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving system health.')
      }
    })

    // Error summary command
    this.bot.onText(/\/errors/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        const errorSummary = monitoring.getErrorSummary()

        if (errorSummary.length === 0) {
          this.bot.sendMessage(message.chat.id, '✅ No errors recorded!')
          return
        }

        const errorMessage = `
🚨 <b>Error Summary</b>

${errorSummary.slice(0, 10).map(error => `
<b>${error.component}</b> (${error.severity})
• Errors: ${error.errorCount}
• Last: ${error.lastError.toLocaleString()}
• Types: ${Object.entries(error.errorTypes).map(([type, count]) => `${type}:${count}`).join(', ')}
`).join('\n')}

💡 <b>Commands:</b>
/health - System health
/logs - View recent logs
/monitoring_config - View logging config
        `

        await this.bot.sendMessage(message.chat.id, errorMessage, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error getting error summary:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving error summary.')
      }
    })

    // Logs command
    this.bot.onText(/\/logs/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        const logInfo = monitoring.exportLogs()
        await this.bot.sendMessage(message.chat.id, logInfo, { parse_mode: 'HTML' })

        // Optionally send recent error log file
        if (process.env.ENABLE_FILE_LOGGING === 'true') {
          const logDir = process.env.LOG_DIRECTORY || 'logs'
          const errorLogFiles = fs.readdirSync(logDir)
            .filter(file => file.startsWith('errors-') && file.endsWith('.log'))
            .sort()
            .reverse()

          if (errorLogFiles.length > 0) {
            const latestErrorLog = path.join(logDir, errorLogFiles[0])

            try {
              // Send last 50 lines of error log
              const logContent = fs.readFileSync(latestErrorLog, 'utf8')
              const lines = logContent.split('\n').slice(-50).join('\n')

              if (lines.trim()) {
                await this.bot.sendMessage(
                  message.chat.id,
                  `📄 <b>Recent Errors (last 50 lines):</b>\n\n<code>${lines.substring(0, 4000)}</code>`,
                  { parse_mode: 'HTML' }
                )
              }
            } catch (fileError) {
              console.error('Error reading log file:', fileError)
            }
          }
        }
      } catch (error) {
        console.error('Error getting logs:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving logs.')
      }
    })

    // Monitoring configuration command
    this.bot.onText(/\/monitoring_config/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        const config = logger.getLoggerConfig()

        const configMessage = `
⚙️ <b>Monitoring Configuration</b>

📊 <b>Logging:</b>
• Level: ${config.logLevel}
• File Logging: ${config.enableFileLogging ? '✅' : '❌'}
• Directory: ${config.logDirectory}
• Max File Size: ${config.maxLogFileSize}MB
• Max Files: ${config.maxLogFiles}

🔍 <b>Error Tracking:</b>
• Error Tracking: ${config.enableErrorTracking ? '✅' : '❌'}
• Console Alerts: ${config.enableConsoleAlerts ? '✅' : '❌'}
• File Alerts: ${config.enableFileAlerts ? '✅' : '❌'}

📈 <b>Performance:</b>
• Performance Logging: ${config.enablePerformanceLogging ? '✅' : '❌'}
• Slow Operation Threshold: ${config.slowOperationThreshold}ms

🌐 <b>API Logging:</b>
• Helius Calls: ${process.env.LOG_HELIUS_CALLS === 'true' ? '✅' : '❌'}
• RPC Calls: ${process.env.LOG_RPC_CALLS === 'true' ? '✅' : '❌'}
• Rate Limits: ${process.env.LOG_RATE_LIMITS === 'true' ? '✅' : '❌'}

📊 <b>Statistics:</b>
• Hourly Stats: ${process.env.ENABLE_STATS_LOGGING === 'true' ? '✅' : '❌'}
• Daily Reports: ${process.env.ENABLE_DAILY_REPORTS === 'true' ? '✅' : '❌'}

💡 <b>Available Commands:</b>
/health - System health status
/errors - Error summary
/logs - View recent logs
/monitoring_test - Test error logging
        `

        await this.bot.sendMessage(message.chat.id, configMessage, { parse_mode: 'HTML' })
      } catch (error) {
        console.error('Error getting monitoring config:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error retrieving monitoring configuration.')
      }
    })

    // Test monitoring command
    this.bot.onText(/\/monitoring_test/, async (message) => {
      const userId = message.from?.id.toString()
      const adminChatId = process.env.ADMIN_CHAT_ID

      if (userId !== adminChatId) {
        this.bot.sendMessage(message.chat.id, '❌ This command is only available to administrators.')
        return
      }

      try {
        // Test different log levels
        logger.debug('Test debug message', { component: 'monitoring-test' })
        logger.info('Test info message', { component: 'monitoring-test' })
        logger.warn('Test warning message', { component: 'monitoring-test' })
        logger.error('Test error message', new Error('Test error'), { component: 'monitoring-test' })

        // Test performance logging
        const timer = logger.createTimer('test_operation', 'monitoring-test')
        setTimeout(() => {
          timer()
        }, 100)

        // Test error tracking
        monitoring.recordError('monitoring-test', 'medium')

        // Test API logging
        logger.logHeliusCall('/test', 0, false, 1000, new Error('Test API error'))
        logger.logRpcCall('test-endpoint', 'testMethod', true, 500)

        await this.bot.sendMessage(
          message.chat.id,
          `✅ <b>Monitoring Test Completed</b>

🧪 <b>Tests Performed:</b>
• Debug, Info, Warning, Error logs
• Performance timing
• Error tracking
• API call logging

📊 Check logs to verify all systems are working correctly.

💡 Use /logs to see the test entries.`,
          { parse_mode: 'HTML' }
        )

        logger.info('Monitoring test completed', {
          component: 'monitoring-test',
          metadata: { requestedBy: userId }
        })

      } catch (error) {
        console.error('Error running monitoring test:', error)
        this.bot.sendMessage(message.chat.id, '❌ Error running monitoring test.')
      }
    })

    console.log(chalk.greenBright('📊 Monitoring admin commands registered'))
  }

  // Button handlers for inline keyboard
  public async handleHealthButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    const adminChatId = process.env.ADMIN_CHAT_ID

    // Check if user is admin
    if (userId !== adminChatId) {
      this.bot.sendMessage(message.chat.id, '❌ This feature is only available to administrators.')
      return
    }

    try {
      const healthSummary = monitoring.getHealthSummary()
      await this.bot.sendMessage(message.chat.id, healthSummary, { parse_mode: 'HTML' })
    } catch (error) {
      console.error('Error getting health summary:', error)
      this.bot.sendMessage(message.chat.id, '❌ Error retrieving system health.')
    }
  }

  public async handleErrorsButton(message: TelegramBot.Message): Promise<void> {
    const userId = message.from?.id.toString()
    const adminChatId = process.env.ADMIN_CHAT_ID

    if (userId !== adminChatId) {
      this.bot.sendMessage(message.chat.id, '❌ This feature is only available to administrators.')
      return
    }

    try {
      const errorSummary = monitoring.getErrorSummary()

      if (errorSummary.length === 0) {
        this.bot.sendMessage(message.chat.id, '✅ No errors recorded!')
        return
      }

      const errorMessage = `
🚨 <b>Error Summary - Enhanced Edition</b>

${errorSummary.slice(0, 10).map(error => `
<b>${error.component}</b> (${error.severity})
• Errors: ${error.errorCount}
• Last: ${error.lastError.toLocaleString()}
• Types: ${Object.entries(error.errorTypes).map(([type, count]) => `${type}:${count}`).join(', ')}
`).join('\n')}

💡 <b>Admin Commands:</b>
/health - System health status
/logs - View recent logs
/monitoring_config - View logging config
      `

      await this.bot.sendMessage(message.chat.id, errorMessage, { parse_mode: 'HTML' })
    } catch (error) {
      console.error('Error getting error summary:', error)
      this.bot.sendMessage(message.chat.id, '❌ Error retrieving error summary.')
    }
  }
}
